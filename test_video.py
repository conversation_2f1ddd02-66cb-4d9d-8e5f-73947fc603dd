#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تحميل الفيديو وتشخيص المشاكل
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QFileDialog, QMessageBox
from PyQt5.QtCore import QUrl
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent

def test_video_file():
    """اختبار ملف فيديو"""
    app = QApplication(sys.argv)
    
    # اختيار ملف فيديو
    file_path, _ = QFileDialog.getOpenFileName(
        None, "اختر ملف فيديو للاختبار",
        os.path.expanduser("~"),
        "ملفات الفيديو (*.mp4 *.avi *.mkv *.mov *.wmv *.flv *.webm *.m4v *.3gp);;جميع الملفات (*.*)"
    )
    
    if not file_path:
        print("لم يتم اختيار ملف")
        return
    
    print(f"اختبار الملف: {file_path}")
    print("=" * 50)
    
    # معلومات الملف
    print(f"📁 مسار الملف: {file_path}")
    print(f"📄 اسم الملف: {os.path.basename(file_path)}")
    print(f"📂 مجلد الملف: {os.path.dirname(file_path)}")
    print(f"🔗 امتداد الملف: {os.path.splitext(file_path)[1]}")
    print(f"📊 حجم الملف: {os.path.getsize(file_path) / (1024*1024):.1f} ميجابايت")
    print(f"✅ الملف موجود: {os.path.exists(file_path)}")
    print()
    
    # اختبار QMediaPlayer
    print("🎬 اختبار QMediaPlayer...")
    player = QMediaPlayer()
    
    # ربط الإشارات
    def on_media_status_changed(status):
        status_names = {
            QMediaPlayer.NoMedia: "لا يوجد وسائط",
            QMediaPlayer.LoadingMedia: "جاري التحميل",
            QMediaPlayer.LoadedMedia: "تم التحميل بنجاح ✅",
            QMediaPlayer.StalledMedia: "توقف التحميل",
            QMediaPlayer.BufferingMedia: "جاري التخزين المؤقت",
            QMediaPlayer.BufferedMedia: "تم التخزين المؤقت",
            QMediaPlayer.EndOfMedia: "انتهت الوسائط",
            QMediaPlayer.InvalidMedia: "وسائط غير صالحة ❌"
        }
        status_name = status_names.get(status, f"حالة غير معروفة: {status}")
        print(f"   حالة الوسائط: {status_name}")
    
    def on_error(error):
        error_names = {
            QMediaPlayer.NoError: "لا يوجد خطأ",
            QMediaPlayer.ResourceError: "خطأ في المورد ❌",
            QMediaPlayer.FormatError: "تنسيق غير مدعوم ❌",
            QMediaPlayer.NetworkError: "خطأ في الشبكة ❌",
            QMediaPlayer.AccessDeniedError: "ممنوع الوصول ❌"
        }
        error_name = error_names.get(error, f"خطأ غير معروف: {error}")
        print(f"   خطأ: {error_name}")
        
        if error == QMediaPlayer.FormatError:
            print("   💡 نصيحة: جرب تحويل الفيديو إلى MP4")
        elif error == QMediaPlayer.ResourceError:
            print("   💡 نصيحة: تأكد من صحة مسار الملف")
    
    player.mediaStatusChanged.connect(on_media_status_changed)
    player.error.connect(on_error)
    
    # تحميل الملف
    file_url = QUrl.fromLocalFile(os.path.abspath(file_path))
    media_content = QMediaContent(file_url)
    player.setMedia(media_content)
    
    print(f"   URL: {file_url.toString()}")
    print()
    
    # انتظار قليل للتحميل
    import time
    time.sleep(2)
    
    # معلومات إضافية
    print("📋 معلومات إضافية:")
    print(f"   مدة الفيديو: {player.duration()} مللي ثانية")
    print(f"   حالة المشغل: {player.state()}")
    print(f"   حالة الوسائط: {player.mediaStatus()}")
    print(f"   خطأ: {player.error()}")
    print()
    
    # اختبار تنسيقات مدعومة
    print("🎯 التنسيقات المدعومة:")
    supported_formats = ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp']
    file_ext = os.path.splitext(file_path)[1].lower()
    
    for fmt in supported_formats:
        status = "✅" if fmt == file_ext else "⚪"
        current = " (الملف الحالي)" if fmt == file_ext else ""
        print(f"   {status} {fmt}{current}")
    
    print()
    print("=" * 50)
    
    if player.mediaStatus() == QMediaPlayer.LoadedMedia:
        print("🎉 نجح تحميل الفيديو! يمكن استخدامه في البرنامج.")
    else:
        print("❌ فشل تحميل الفيديو.")
        print()
        print("💡 حلول مقترحة:")
        print("1. جرب ملف MP4 بدلاً من التنسيق الحالي")
        print("2. تأكد من أن الملف غير تالف")
        print("3. جرب ملف فيديو آخر")
        print("4. تأكد من تثبيت جميع codecs المطلوبة")
    
    # عرض رسالة
    if player.mediaStatus() == QMediaPlayer.LoadedMedia:
        QMessageBox.information(None, "نتيجة الاختبار", "✅ نجح تحميل الفيديو!")
    else:
        QMessageBox.warning(None, "نتيجة الاختبار", "❌ فشل تحميل الفيديو. راجع التفاصيل في Terminal.")

if __name__ == "__main__":
    test_video_file()
