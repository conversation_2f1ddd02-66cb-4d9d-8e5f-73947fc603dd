#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة إدارة الإعدادات
"""

import os
import json
from pathlib import Path

class Config:
    """فئة إدارة إعدادات البرنامج"""
    
    def __init__(self):
        """تهيئة الإعدادات"""
        # المسار الافتراضي لملف الإعدادات
        self.config_dir = os.path.join(str(Path.home()), ".media_translator")
        self.config_file = os.path.join(self.config_dir, "config.json")
        
        # إنشاء مجلد الإعدادات إذا لم يكن موجودًا
        if not os.path.exists(self.config_dir):
            os.makedirs(self.config_dir)
            
        # إنشاء مجلدات البيانات
        self.data_dir = os.path.join(self.config_dir, "data")
        self.models_dir = os.path.join(self.config_dir, "models")
        self.cache_dir = os.path.join(self.config_dir, "cache")
        self.output_dir = os.path.join(self.config_dir, "output")
        
        for directory in [self.data_dir, self.models_dir, self.cache_dir, self.output_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)
        
        # الإعدادات الافتراضية
        self.default_config = {
            "general": {
                "language": "ar",
                "theme": "dark",
                "save_history": True,
                "auto_check_updates": True,
                "recent_files": []
            },
            "player": {
                "volume": 70,
                "remember_position": True,
                "subtitle_enabled": True,
                "subtitle_font_size": 14,
                "subtitle_color": "#FFFFFF"
            },
            "translation": {
                "source_language": "auto",
                "target_language": "ar",
                "whisper_model": "medium",
                "translation_model": "nllb",
                "tts_model": "tts_models/ar/fairseq/tts-transformer",
                "voice_gender": "male",
                "speech_rate": 1.0,
                "auto_sync": True
            },
            "processing": {
                "use_gpu": True,
                "chunk_size": 30,  # بالثواني
                "parallel_processing": True,
                "cache_results": True,
                "quality": "high"
            },
            "paths": {
                "last_open_dir": str(Path.home()),
                "last_save_dir": str(Path.home()),
                "custom_ffmpeg_path": ""
            }
        }
        
        # تحميل الإعدادات
        self.config = self.load_config()
    
    def load_config(self):
        """تحميل الإعدادات من الملف"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # دمج الإعدادات المحملة مع الإعدادات الافتراضية
                merged_config = self.default_config.copy()
                self._deep_update(merged_config, config)
                return merged_config
            except Exception as e:
                print(f"خطأ في تحميل ملف الإعدادات: {e}")
                return self.default_config
        else:
            # إنشاء ملف إعدادات جديد
            self.save_config(self.default_config)
            return self.default_config
    
    def save_config(self, config=None):
        """حفظ الإعدادات في الملف"""
        if config is None:
            config = self.config
            
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"خطأ في حفظ ملف الإعدادات: {e}")
            return False
    
    def get(self, section, key, default=None):
        """الحصول على قيمة إعداد معين"""
        try:
            return self.config[section][key]
        except KeyError:
            return default
    
    def set(self, section, key, value):
        """تعيين قيمة إعداد معين"""
        if section not in self.config:
            self.config[section] = {}
        
        self.config[section][key] = value
        self.save_config()
    
    def add_recent_file(self, file_path):
        """إضافة ملف إلى قائمة الملفات الأخيرة"""
        recent_files = self.get("general", "recent_files", [])
        
        # إزالة الملف إذا كان موجودًا بالفعل
        if file_path in recent_files:
            recent_files.remove(file_path)
        
        # إضافة الملف في بداية القائمة
        recent_files.insert(0, file_path)
        
        # الاحتفاظ بآخر 10 ملفات فقط
        recent_files = recent_files[:10]
        
        self.set("general", "recent_files", recent_files)
    
    def _deep_update(self, d, u):
        """تحديث القاموس بشكل عميق"""
        for k, v in u.items():
            if isinstance(v, dict) and k in d and isinstance(d[k], dict):
                self._deep_update(d[k], v)
            else:
                d[k] = v
