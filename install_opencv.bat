@echo off
title تثبيت OpenCV لحل مشاكل الفيديو

echo ============================================================
echo تثبيت OpenCV لحل مشاكل تشغيل الفيديو
echo ============================================================
echo.

echo 💡 OpenCV هو مكتبة قوية لمعالجة الفيديو والصور
echo    سيتم استخدامها كبديل لـ Qt Multimedia
echo.

REM التحقق من وجود Python
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ❌ خطأ: لم يتم العثور على Python
    echo.
    pause
    exit /b 1
)

echo ✅ Python متاح
echo.

echo 🔄 تثبيت OpenCV...
echo.

REM تثبيت opencv-python
echo تثبيت opencv-python...
python -m pip install opencv-python

if %ERRORLEVEL% neq 0 (
    echo ❌ فشل في تثبيت opencv-python
    echo.
    echo 💡 جرب:
    echo    pip install --upgrade pip
    echo    pip install opencv-python
    echo.
    pause
    exit /b 1
)

echo ✅ تم تثبيت opencv-python بنجاح
echo.

REM اختبار التثبيت
echo 🧪 اختبار التثبيت...
python -c "import cv2; print(f'OpenCV version: {cv2.__version__}')"

if %ERRORLEVEL% neq 0 (
    echo ❌ فشل في اختبار OpenCV
    pause
    exit /b 1
)

echo ✅ OpenCV يعمل بشكل صحيح
echo.

echo ============================================================
echo 🎉 تم تثبيت OpenCV بنجاح!
echo ============================================================
echo.
echo الآن يمكن للبرنامج استخدام OpenCV كبديل لتشغيل الفيديو
echo هذا سيحل مشاكل DirectShow والمسارات العربية
echo.

pause
