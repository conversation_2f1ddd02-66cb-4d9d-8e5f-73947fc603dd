#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
محرك الترجمة
"""

import os
import time
import torch
from transformers import Marian<PERSON>Model, MarianTokenizer
from core.simple_media_processor import SimpleMediaProcessor

class TranslationEngine:
    """فئة محرك الترجمة"""

    def __init__(self, config):
        """تهيئة محرك الترجمة"""
        self.config = config
        self.media_processor = SimpleMediaProcessor(config)
        self.translation_models = {}

    def extract_text(self, video_path, model=None, language=None, progress_callback=None):
        """استخراج النص من الفيديو"""
        try:
            if progress_callback:
                progress_callback(0, "جاري استخراج الصوت من الفيديو...")

            # استخراج الصوت من الفيديو
            audio_path = self.media_processor.extract_audio(
                video_path,
                progress_callback=lambda p, m: progress_callback(p * 0.3, m)
            )

            if not audio_path:
                if progress_callback:
                    progress_callback(0, "فشل في استخراج الصوت من الفيديو")
                return None

            if progress_callback:
                progress_callback(30, "جاري استخراج النص من الصوت...")

            # استخراج النص من الصوت
            result = self.media_processor.transcribe_audio(
                audio_path,
                model=model,
                language=language,
                progress_callback=lambda p, m: progress_callback(30 + p * 0.7, m)
            )

            if not result:
                if progress_callback:
                    progress_callback(0, "فشل في استخراج النص من الصوت")
                return None

            if progress_callback:
                progress_callback(100, "تم استخراج النص بنجاح")

            return {"text": result["text"], "segments": result["segments"]}
        except Exception as e:
            if progress_callback:
                progress_callback(0, f"فشل في استخراج النص: {str(e)}")
            return None

    def translate_text(self, text, source_lang="auto", target_lang="ar", progress_callback=None):
        """ترجمة النص"""
        try:
            if progress_callback:
                progress_callback(0, "جاري تحضير الترجمة...")

            # تحديد لغة المصدر
            if source_lang == "auto":
                # استخدام الإنجليزية كلغة افتراضية
                source_lang = "en"

            # تحديد زوج اللغات
            lang_pair = f"{source_lang}-{target_lang}"

            # التحقق من دعم زوج اللغات
            if not self._is_supported_language_pair(source_lang, target_lang):
                if progress_callback:
                    progress_callback(0, f"زوج اللغات غير مدعوم: {lang_pair}")
                return None

            # تحميل نموذج الترجمة
            if progress_callback:
                progress_callback(10, "جاري تحميل نموذج الترجمة...")

            model, tokenizer = self._load_translation_model(source_lang, target_lang)

            if not model or not tokenizer:
                if progress_callback:
                    progress_callback(0, "فشل في تحميل نموذج الترجمة")
                return None

            if progress_callback:
                progress_callback(30, "جاري ترجمة النص...")

            # تقسيم النص إلى جمل
            sentences = self._split_into_sentences(text)

            # ترجمة كل جملة
            translated_sentences = []
            total_sentences = len(sentences)

            for i, sentence in enumerate(sentences):
                # ترجمة الجملة
                translated = self._translate_sentence(sentence, model, tokenizer)
                translated_sentences.append(translated)

                # تحديث التقدم
                if progress_callback:
                    progress = 30 + (i + 1) / total_sentences * 70
                    progress_callback(progress, f"جاري ترجمة النص... ({i+1}/{total_sentences})")

            # دمج الجمل المترجمة
            translated_text = " ".join(translated_sentences)

            if progress_callback:
                progress_callback(100, "تم ترجمة النص بنجاح")

            return {"text": translated_text}
        except Exception as e:
            if progress_callback:
                progress_callback(0, f"فشل في ترجمة النص: {str(e)}")
            return None

    def _is_supported_language_pair(self, source_lang, target_lang):
        """التحقق من دعم زوج اللغات"""
        # قائمة أزواج اللغات المدعومة
        supported_pairs = [
            ("en", "ar"), ("ar", "en"),
            ("en", "fr"), ("fr", "en"),
            ("en", "es"), ("es", "en"),
            ("en", "de"), ("de", "en"),
            ("en", "it"), ("it", "en"),
            ("en", "ru"), ("ru", "en"),
            ("en", "zh"), ("zh", "en"),
            ("en", "ja"), ("ja", "en"),
        ]

        return (source_lang, target_lang) in supported_pairs

    def _get_model_name(self, source_lang, target_lang):
        """الحصول على اسم نموذج الترجمة"""
        # تعيين اسم النموذج بناءً على زوج اللغات
        lang_map = {
            "en": "en",
            "ar": "ar",
            "fr": "fr",
            "es": "es",
            "de": "de",
            "it": "it",
            "ru": "ru",
            "zh": "zh",
            "ja": "jap",
        }

        source = lang_map.get(source_lang, source_lang)
        target = lang_map.get(target_lang, target_lang)

        return f"Helsinki-NLP/opus-mt-{source}-{target}"

    def _load_translation_model(self, source_lang, target_lang):
        """تحميل نموذج الترجمة المحلي"""
        try:
            # تحديد اسم النموذج
            model_name = self._get_model_name(source_lang, target_lang)

            # التحقق من وجود النموذج في الذاكرة
            if model_name in self.translation_models:
                return self.translation_models[model_name]

            # مجلد النماذج المحلي
            models_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "models", "translation")
            os.makedirs(models_dir, exist_ok=True)

            model_path = os.path.join(models_dir, model_name.replace("/", "_"))

            # تحميل النموذج
            if os.path.exists(model_path):
                # تحميل من الملفات المحلية
                tokenizer = MarianTokenizer.from_pretrained(model_path)
                model = MarianMTModel.from_pretrained(model_path)
                print(f"تم تحميل النموذج من الملفات المحلية: {model_path}")
            else:
                # تنزيل النموذج وحفظه محلياً
                print(f"جاري تنزيل النموذج: {model_name}")
                tokenizer = MarianTokenizer.from_pretrained(model_name)
                model = MarianMTModel.from_pretrained(model_name)

                # حفظ النموذج محلياً
                tokenizer.save_pretrained(model_path)
                model.save_pretrained(model_path)
                print(f"تم حفظ النموذج محلياً: {model_path}")

            # حفظ النموذج في الذاكرة
            self.translation_models[model_name] = (model, tokenizer)

            return model, tokenizer
        except Exception as e:
            print(f"فشل في تحميل نموذج الترجمة: {str(e)}")
            return None, None

    def _split_into_sentences(self, text):
        """تقسيم النص إلى جمل"""
        # تقسيم النص إلى جمل بناءً على علامات الترقيم
        import re
        sentences = re.split(r'(?<=[.!?])\s+', text)

        # التعامل مع الجمل الطويلة
        max_length = 100  # الحد الأقصى لطول الجملة
        result = []

        for sentence in sentences:
            if len(sentence) <= max_length:
                result.append(sentence)
            else:
                # تقسيم الجملة الطويلة إلى أجزاء
                words = sentence.split()
                current_part = []
                current_length = 0

                for word in words:
                    if current_length + len(word) + 1 <= max_length:
                        current_part.append(word)
                        current_length += len(word) + 1
                    else:
                        result.append(" ".join(current_part))
                        current_part = [word]
                        current_length = len(word)

                if current_part:
                    result.append(" ".join(current_part))

        return result

    def _translate_sentence(self, sentence, model, tokenizer):
        """ترجمة جملة واحدة"""
        try:
            # ترميز الجملة
            encoded = tokenizer(sentence, return_tensors="pt", padding=True)

            # ترجمة الجملة
            with torch.no_grad():
                output = model.generate(**encoded)

            # فك ترميز الترجمة
            translated = tokenizer.decode(output[0], skip_special_tokens=True)

            return translated
        except Exception as e:
            print(f"فشل في ترجمة الجملة: {str(e)}")
            return sentence
