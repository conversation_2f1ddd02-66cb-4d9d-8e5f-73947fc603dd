🎬 دليل حل مشاكل تشغيل الفيديو
========================================

🔍 المشكلة الحالية:
-------------------
البرنامج لا يستطيع تشغيل ملفات MP4 بسبب:
1. المسارات التي تحتوي على أحرف عربية
2. مشاكل في DirectShow codec (خطأ 0x80040266)

🛠️ الحلول المتاحة:
------------------

الحل الأول: تغيير مسار الملفات
--------------------------------
1. انسخ ملفات الفيديو إلى مجلد بمسار إنجليزي مثل:
   C:\Videos\
   D:\MyVideos\

2. تأكد من أن اسم الملف لا يحتوي على أحرف عربية
   مثال: بدلاً من "درس 1.mp4" استخدم "lesson1.mp4"

الحل الثاني: استخدام أداة الاختبار
----------------------------------
1. شغل ملف "اختبار_الفيديو.bat"
2. اختر ملف الفيديو
3. جرب "اختبار مع نسخة مؤقتة"
4. إذا نجح، فالمشكلة في المسار

الحل الثالث: تحويل تنسيق الفيديو
--------------------------------
استخدم ffmpeg لتحويل الفيديو:

ffmpeg -i "input.mp4" -c:v libx264 -c:a aac -preset fast -crf 23 "output.mp4"

الحل الرابع: تثبيت K-Lite Codec Pack
-----------------------------------
1. حمل K-Lite Codec Pack من الموقع الرسمي
2. ثبته مع الإعدادات الافتراضية
3. أعد تشغيل الكمبيوتر
4. جرب البرنامج مرة أخرى

🧪 اختبار سريع:
---------------
1. شغل "اختبار_الفيديو.bat"
2. اختر ملف MP4
3. لاحظ الرسائل في وحدة التحكم
4. إذا ظهر "تم التحميل بنجاح" فالملف يعمل
5. إذا ظهر "ملف غير صالح" فهناك مشكلة في الملف أو codec

📋 معلومات تقنية:
-----------------
خطأ DirectShow 0x80040266 يعني:
- مشكلة في codec المطلوب
- تنسيق الملف غير مدعوم
- مشكلة في مسار الملف

🎯 التوصيات:
-------------
للحصول على أفضل توافق:

1. استخدم ملفات MP4 مع:
   - Video: H.264 codec
   - Audio: AAC codec
   - Container: MP4

2. تجنب:
   - المسارات العربية
   - الأسماء العربية للملفات
   - المسارات الطويلة جداً

3. استخدم مجلدات بسيطة مثل:
   C:\Videos\
   D:\Movies\

🔧 إصلاح تلقائي:
----------------
البرنامج الآن يحتوي على:
- إنشاء نسخ مؤقتة تلقائياً للملفات ذات المسارات العربية
- تشخيص مفصل للمشاكل
- رسائل واضحة عن سبب فشل التحميل

💡 نصائح إضافية:
-----------------
1. تأكد من أن الملف غير تالف
2. جرب ملفات مختلفة للتأكد
3. تحقق من مساحة القرص الصلب
4. أغلق برامج أخرى قد تستخدم الملف

🆘 إذا لم تنجح الحلول:
---------------------
1. شغل "python fix_video_loading.py <مسار_الملف>"
2. أرسل تفاصيل الخطأ من وحدة التحكم
3. جرب ملف فيديو آخر للمقارنة

📞 للمساعدة:
-------------
إذا استمرت المشكلة، أرسل:
1. نوع ملف الفيديو (codec, format)
2. مسار الملف
3. رسائل الخطأ من وحدة التحكم
4. نتائج اختبار "اختبار_الفيديو.bat"
