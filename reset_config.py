#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إعادة تعيين إعدادات البرنامج للقيم الافتراضية
"""

import os
import json

def reset_config():
    """إعادة تعيين الإعدادات"""
    
    # مسار ملف الإعدادات
    home_dir = os.path.expanduser("~")
    config_dir = os.path.join(home_dir, ".media_translator")
    config_file = os.path.join(config_dir, "config.json")
    
    print("🔄 إعادة تعيين إعدادات البرنامج...")
    
    # حذف ملف الإعدادات الحالي إذا كان موجود<|im_start|>
    if os.path.exists(config_file):
        try:
            os.remove(config_file)
            print(f"✅ تم حذف ملف الإعدادات القديم: {config_file}")
        except Exception as e:
            print(f"❌ فشل في حذف ملف الإعدادات: {str(e)}")
    
    # إنشاء إعدادات افتراضية جديدة
    default_config = {
        "general": {
            "theme": "dark",
            "recent_files": []
        },
        "translation": {
            "source_language": "auto",
            "target_language": "ar",
            "whisper_model": "medium",  # التأكد من استخدام medium
            "tts_model": "tts_models/ar/fairseq/tts-transformer",
            "voice_gender": "male",
            "speech_rate": 1.0
        },
        "processing": {
            "quality": "high",
            "chunk_size": 30
        },
        "player": {
            "volume": 70
        },
        "paths": {
            "models_dir": os.path.join(home_dir, ".media_translator", "models"),
            "temp_dir": os.path.join(home_dir, ".media_translator", "temp"),
            "output_dir": os.path.join(home_dir, "Documents")
        },
        "models": {
            "models_dir": os.path.join(os.path.dirname(__file__), "models"),
            "whisper_models_dir": os.path.join(os.path.dirname(__file__), "models", "whisper"),
            "translation_models_dir": os.path.join(os.path.dirname(__file__), "models", "translation"),
            "tts_models_dir": os.path.join(os.path.dirname(__file__), "models", "tts"),
            "whisper_medium_path": "",
            "translation_en_ar_path": "",
            "translation_ar_en_path": "",
            "tts_arabic_path": "",
            "tts_english_path": ""
        }
    }
    
    # إنشاء مجلد الإعدادات إذا لم يكن موجود<|im_start|>
    if not os.path.exists(config_dir):
        os.makedirs(config_dir)
        print(f"✅ تم إنشاء مجلد الإعدادات: {config_dir}")
    
    # حفظ الإعدادات الجديدة
    try:
        with open(config_file, "w", encoding="utf-8") as f:
            json.dump(default_config, f, ensure_ascii=False, indent=4)
        print(f"✅ تم إنشاء ملف إعدادات جديد: {config_file}")
        print("✅ تم تعيين نموذج Whisper إلى 'medium'")
    except Exception as e:
        print(f"❌ فشل في إنشاء ملف الإعدادات: {str(e)}")
    
    print("\n🎉 تم إعادة تعيين الإعدادات بنجاح!")
    print("يمكنك الآن تشغيل البرنامج باستخدام تشغيل.bat")

if __name__ == "__main__":
    reset_config()
    
    print("\nاضغط Enter للخروج...")
    input()
