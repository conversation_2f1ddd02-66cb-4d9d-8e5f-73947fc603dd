@echo off
echo ============================================================
echo تثبيت المكتبات المطلوبة للبرنامج
echo ============================================================
echo.

REM التحقق من وجود Python
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo خطأ: لم يتم العثور على Python.
    echo يرجى تثبيت Python 3.8 أو أحدث من https://www.python.org/downloads/
    pause
    exit /b 1
)

echo تم العثور على Python:
python --version
echo.

echo جاري تثبيت المكتبات الأساسية...
echo.

REM تحديث pip
echo 1/8 - تحديث pip...
python -m pip install --upgrade pip

REM تثبيت المكتبات الأساسية
echo 2/8 - تثبيت PyQt5...
python -m pip install PyQt5

echo 3/8 - تثبيت numpy...
python -m pip install numpy

echo 4/8 - تثبيت pydub...
python -m pip install pydub

echo 5/8 - تثبيت openai-whisper...
python -m pip install openai-whisper

echo 6/8 - تثبيت gtts...
python -m pip install gtts

echo 7/8 - تثبيت transformers...
python -m pip install transformers torch

echo 8/8 - تثبيت TTS (اختياري)...
python -m pip install TTS

echo.
echo ============================================================
echo ✅ تم تثبيت جميع المكتبات بنجاح!
echo يمكنك الآن تشغيل البرنامج باستخدام run.bat
echo ============================================================
echo.
pause
