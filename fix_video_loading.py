#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح مشكلة تحميل الفيديوهات
"""

import os
import sys
import shutil
import tempfile
import subprocess
from pathlib import Path

def create_temp_copy(video_path):
    """إنشاء نسخة مؤقتة من الفيديو بمسار إنجليزي"""
    try:
        print(f"🔄 إنشاء نسخة مؤقتة من الفيديو...")
        
        # إنشاء مجلد مؤقت
        temp_dir = tempfile.mkdtemp(prefix="video_temp_")
        
        # الحصول على امتداد الملف
        file_ext = os.path.splitext(video_path)[1]
        
        # إنشاء اسم ملف مؤقت بسيط
        temp_filename = f"temp_video{file_ext}"
        temp_path = os.path.join(temp_dir, temp_filename)
        
        # نسخ الملف
        print(f"📁 نسخ من: {video_path}")
        print(f"📁 إلى: {temp_path}")
        
        shutil.copy2(video_path, temp_path)
        
        if os.path.exists(temp_path):
            file_size = os.path.getsize(temp_path) / (1024 * 1024)
            print(f"✅ تم إنشاء النسخة المؤقتة ({file_size:.1f} MB)")
            return temp_path
        else:
            print("❌ فشل في إنشاء النسخة المؤقتة")
            return None
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة المؤقتة: {str(e)}")
        return None

def test_video_with_ffmpeg(video_path):
    """اختبار الفيديو باستخدام ffmpeg"""
    try:
        print(f"🧪 اختبار الفيديو باستخدام ffmpeg...")
        
        # اختبار معلومات الفيديو
        cmd = [
            "ffmpeg", "-i", video_path,
            "-t", "1", "-f", "null", "-"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ الفيديو صالح ومدعوم من ffmpeg")
            
            # استخراج معلومات الفيديو
            info_cmd = ["ffprobe", "-v", "quiet", "-print_format", "json", "-show_format", "-show_streams", video_path]
            info_result = subprocess.run(info_cmd, capture_output=True, text=True, timeout=10)
            
            if info_result.returncode == 0:
                print("📊 معلومات الفيديو متاحة")
                return True
            
        else:
            print(f"❌ الفيديو غير صالح أو غير مدعوم: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ انتهت مهلة اختبار الفيديو")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار الفيديو: {str(e)}")
        return False

def convert_video_format(input_path, output_path=None):
    """تحويل الفيديو إلى تنسيق متوافق"""
    try:
        if output_path is None:
            # إنشاء مسار الإخراج
            temp_dir = tempfile.mkdtemp(prefix="converted_video_")
            output_path = os.path.join(temp_dir, "converted_video.mp4")
        
        print(f"🔄 تحويل الفيديو إلى تنسيق متوافق...")
        print(f"📁 من: {input_path}")
        print(f"📁 إلى: {output_path}")
        
        # أمر التحويل مع إعدادات متوافقة
        cmd = [
            "ffmpeg", "-i", input_path,
            "-c:v", "libx264",           # H.264 codec
            "-c:a", "aac",               # AAC audio
            "-preset", "fast",           # سرعة التحويل
            "-crf", "23",                # جودة جيدة
            "-movflags", "+faststart",   # تحسين للتشغيل
            "-y",                        # استبدال الملف الموجود
            output_path
        ]
        
        print("⏳ جاري التحويل... (قد يستغرق بعض الوقت)")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0 and os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024 * 1024)
            print(f"✅ تم التحويل بنجاح ({file_size:.1f} MB)")
            return output_path
        else:
            print(f"❌ فشل في التحويل: {result.stderr}")
            return None
            
    except subprocess.TimeoutExpired:
        print("❌ انتهت مهلة التحويل")
        return None
    except Exception as e:
        print(f"❌ خطأ في التحويل: {str(e)}")
        return None

def fix_video_loading_issue(video_path):
    """إصلاح مشكلة تحميل الفيديو"""
    print("🔧 إصلاح مشكلة تحميل الفيديو")
    print("=" * 50)
    
    if not os.path.exists(video_path):
        print(f"❌ الملف غير موجود: {video_path}")
        return None
    
    # 1. اختبار الفيديو الأصلي
    print("1️⃣ اختبار الفيديو الأصلي...")
    if test_video_with_ffmpeg(video_path):
        print("✅ الفيديو صالح")
    else:
        print("❌ الفيديو غير صالح أو تالف")
        return None
    
    # 2. التحقق من المسار
    print("\n2️⃣ فحص المسار...")
    has_arabic = any(ord(char) > 127 for char in video_path)
    
    if has_arabic:
        print("⚠️ المسار يحتوي على أحرف عربية")
        print("🔄 إنشاء نسخة مؤقتة بمسار إنجليزي...")
        
        temp_path = create_temp_copy(video_path)
        if temp_path:
            print("✅ تم إنشاء نسخة مؤقتة")
            working_path = temp_path
        else:
            print("❌ فشل في إنشاء النسخة المؤقتة")
            return None
    else:
        print("✅ المسار لا يحتوي على أحرف عربية")
        working_path = video_path
    
    # 3. تحويل التنسيق إذا لزم الأمر
    print("\n3️⃣ تحويل التنسيق...")
    converted_path = convert_video_format(working_path)
    
    if converted_path:
        print("✅ تم تحويل الفيديو بنجاح")
        return converted_path
    else:
        print("❌ فشل في تحويل الفيديو")
        # إرجاع المسار المؤقت على الأقل
        return working_path if has_arabic else video_path

def main():
    """الدالة الرئيسية"""
    if len(sys.argv) < 2:
        print("الاستخدام: python fix_video_loading.py <مسار_الفيديو>")
        return
    
    video_path = sys.argv[1]
    
    print("🎬 أداة إصلاح مشاكل تحميل الفيديو")
    print("=" * 60)
    print(f"📁 الملف: {video_path}")
    print()
    
    fixed_path = fix_video_loading_issue(video_path)
    
    if fixed_path:
        print("\n" + "=" * 60)
        print("🎉 تم إصلاح المشكلة!")
        print(f"📁 الملف الجديد: {fixed_path}")
        print("\n💡 يمكنك الآن استخدام هذا الملف في البرنامج")
        
        # اختبار الملف المُصلح
        print("\n🧪 اختبار الملف المُصلح...")
        if test_video_with_ffmpeg(fixed_path):
            print("✅ الملف المُصلح يعمل بشكل صحيح")
        else:
            print("⚠️ قد تكون هناك مشاكل في الملف المُصلح")
    else:
        print("\n" + "=" * 60)
        print("❌ لم يتم إصلاح المشكلة")
        print("💡 تأكد من:")
        print("   • صحة ملف الفيديو")
        print("   • تثبيت ffmpeg بشكل صحيح")
        print("   • وجود مساحة كافية على القرص")

if __name__ == "__main__":
    main()
