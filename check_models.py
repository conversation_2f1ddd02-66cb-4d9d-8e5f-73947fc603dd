#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
فحص شامل لجميع النماذج المطلوبة
"""

import os
import sys
from pathlib import Path

def check_whisper_models():
    """فحص نماذج Whisper"""
    print("📝 فحص نماذج Whisper:")
    print("-" * 30)
    
    # المسارات المحتملة
    local_models_dir = Path(__file__).parent / "models" / "whisper"
    cache_dir = Path.home() / ".cache" / "whisper"
    
    models_to_check = ["tiny", "base", "small", "medium", "large"]
    found_models = []
    
    for model in models_to_check:
        # فحص المجلد المحلي
        local_path = local_models_dir / f"{model}.pt"
        cache_path = cache_dir / f"{model}.pt"
        
        if local_path.exists():
            size_mb = local_path.stat().st_size / (1024 * 1024)
            print(f"   ✅ {model}: موجود محلياً ({size_mb:.1f} MB)")
            found_models.append(model)
        elif cache_path.exists():
            size_mb = cache_path.stat().st_size / (1024 * 1024)
            print(f"   ✅ {model}: موجود في cache ({size_mb:.1f} MB)")
            found_models.append(model)
        else:
            print(f"   ❌ {model}: غير موجود")
    
    print(f"\nالنماذج الموجودة: {len(found_models)}/{len(models_to_check)}")
    return found_models

def check_translation_models():
    """فحص نماذج الترجمة"""
    print("\n🌐 فحص نماذج الترجمة:")
    print("-" * 30)
    
    models_dir = Path(__file__).parent / "models" / "translation"
    cache_dir = Path.home() / ".cache" / "huggingface" / "hub"
    
    models_to_check = [
        ("en-ar", "Helsinki-NLP/opus-mt-en-ar"),
        ("ar-en", "Helsinki-NLP/opus-mt-ar-en"),
        ("en-fr", "Helsinki-NLP/opus-mt-en-fr")
    ]
    
    found_models = []
    
    for model_key, model_name in models_to_check:
        # فحص المجلد المحلي
        local_path = models_dir / model_key
        
        if local_path.exists() and any(local_path.iterdir()):
            # حساب حجم النموذج
            total_size = sum(f.stat().st_size for f in local_path.rglob('*') if f.is_file())
            size_mb = total_size / (1024 * 1024)
            print(f"   ✅ {model_key}: موجود محلياً ({size_mb:.1f} MB)")
            found_models.append(model_key)
        else:
            # فحص cache
            cache_model_dir = cache_dir / f"models--{model_name.replace('/', '--')}"
            if cache_model_dir.exists():
                total_size = sum(f.stat().st_size for f in cache_model_dir.rglob('*') if f.is_file())
                size_mb = total_size / (1024 * 1024)
                print(f"   ✅ {model_key}: موجود في cache ({size_mb:.1f} MB)")
                found_models.append(model_key)
            else:
                print(f"   ❌ {model_key}: غير موجود")
    
    print(f"\nالنماذج الموجودة: {len(found_models)}/{len(models_to_check)}")
    return found_models

def check_tts_models():
    """فحص نماذج TTS"""
    print("\n🔊 فحص نماذج TTS:")
    print("-" * 30)
    
    # فحص مكتبة TTS
    try:
        import TTS
        print("   ✅ مكتبة TTS مثبتة")
        tts_available = True
    except ImportError:
        print("   ❌ مكتبة TTS غير مثبتة")
        tts_available = False
    
    # فحص gTTS
    try:
        import gtts
        print("   ✅ مكتبة gTTS مثبتة")
        gtts_available = True
    except ImportError:
        print("   ❌ مكتبة gTTS غير مثبتة")
        gtts_available = False
    
    # فحص النماذج المحلية
    tts_dir = Path(__file__).parent / "models" / "tts"
    if tts_dir.exists() and any(tts_dir.iterdir()):
        total_size = sum(f.stat().st_size for f in tts_dir.rglob('*') if f.is_file())
        size_mb = total_size / (1024 * 1024)
        print(f"   ✅ نماذج TTS محلية: موجودة ({size_mb:.1f} MB)")
        local_tts = True
    else:
        print("   ⚠️ نماذج TTS محلية: غير موجودة")
        local_tts = False
    
    return {
        "tts_available": tts_available,
        "gtts_available": gtts_available,
        "local_tts": local_tts
    }

def check_dependencies():
    """فحص المكتبات المطلوبة"""
    print("\n📦 فحص المكتبات المطلوبة:")
    print("-" * 30)
    
    required_packages = [
        ("PyQt5", "PyQt5"),
        ("whisper", "openai-whisper"),
        ("transformers", "transformers"),
        ("torch", "torch"),
        ("pydub", "pydub"),
        ("numpy", "numpy"),
        ("gtts", "gtts"),
        ("TTS", "TTS")
    ]
    
    installed = []
    missing = []
    
    for package, pip_name in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package}: مثبت")
            installed.append(package)
        except ImportError:
            print(f"   ❌ {package}: غير مثبت (pip install {pip_name})")
            missing.append(pip_name)
    
    print(f"\nالمكتبات المثبتة: {len(installed)}/{len(required_packages)}")
    return installed, missing

def check_ffmpeg():
    """فحص ffmpeg"""
    print("\n🎬 فحص ffmpeg:")
    print("-" * 30)
    
    import subprocess
    try:
        result = subprocess.run(["ffmpeg", "-version"], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"   ✅ ffmpeg: {version_line}")
            return True
        else:
            print("   ❌ ffmpeg: غير متاح")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("   ❌ ffmpeg: غير مثبت أو غير موجود في PATH")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔍 فحص شامل للنماذج والمتطلبات")
    print("=" * 50)
    
    # فحص النماذج
    whisper_models = check_whisper_models()
    translation_models = check_translation_models()
    tts_status = check_tts_models()
    
    # فحص المكتبات
    installed, missing = check_dependencies()
    
    # فحص ffmpeg
    ffmpeg_ok = check_ffmpeg()
    
    # الملخص النهائي
    print("\n" + "=" * 50)
    print("📊 ملخص الفحص:")
    print("=" * 50)
    
    # حالة النماذج
    print(f"📝 نماذج Whisper: {len(whisper_models)} موجود")
    if whisper_models:
        print(f"   الموجودة: {', '.join(whisper_models)}")
    
    print(f"🌐 نماذج الترجمة: {len(translation_models)} موجود")
    if translation_models:
        print(f"   الموجودة: {', '.join(translation_models)}")
    
    print(f"🔊 TTS: {'✅' if tts_status['tts_available'] or tts_status['gtts_available'] else '❌'}")
    
    print(f"📦 المكتبات: {len(installed)}/{len(installed) + len(missing)} مثبتة")
    
    print(f"🎬 ffmpeg: {'✅' if ffmpeg_ok else '❌'}")
    
    # التوصيات
    print("\n💡 التوصيات:")
    print("-" * 20)
    
    if not whisper_models:
        print("• شغل download_models.bat لتنزيل نماذج Whisper")
    
    if not translation_models:
        print("• نماذج الترجمة ستُنزل تلقائياً عند أول استخدام")
    
    if missing:
        print(f"• ثبت المكتبات المفقودة: pip install {' '.join(missing)}")
    
    if not ffmpeg_ok:
        print("• ثبت ffmpeg من https://ffmpeg.org/download.html")
    
    if not tts_status['tts_available'] and not tts_status['gtts_available']:
        print("• ثبت إحدى مكتبات TTS: pip install TTS أو pip install gtts")
    
    # حالة الاستعداد
    ready = (len(whisper_models) > 0 and 
             len(translation_models) > 0 and 
             len(missing) == 0 and 
             ffmpeg_ok and 
             (tts_status['tts_available'] or tts_status['gtts_available']))
    
    print(f"\n🎯 حالة البرنامج: {'🎉 جاهز للاستخدام!' if ready else '⚠️ يحتاج إعداد'}")
    
    if ready:
        print("يمكنك الآن استخدام جميع وظائف البرنامج بدون مشاكل!")
    else:
        print("يرجى إكمال الإعدادات المطلوبة أعلاه.")

if __name__ == "__main__":
    main()
    
    print("\nاضغط Enter للخروج...")
    input()
