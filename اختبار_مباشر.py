#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار مباشر لتشغيل الفيديوهات
"""

import os
import sys
import tempfile
import shutil
from PyQt5.QtWidgets import QApp<PERSON>, QWidget, QVBoxLayout, QPushButton, QLabel, QFileDialog
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtMultimediaWidgets import QVideoWidget
from PyQt5.QtCore import QUrl

class VideoTester(QWidget):
    def __init__(self):
        super().__init__()
        self.temp_file = None
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("اختبار تشغيل الفيديو المباشر")
        self.setGeometry(100, 100, 800, 600)
        
        layout = QVBoxLayout()
        
        # زر اختيار الملف
        self.select_btn = QPushButton("اختر ملف فيديو")
        self.select_btn.clicked.connect(self.select_file)
        layout.addWidget(self.select_btn)
        
        # تسمية لعرض المعلومات
        self.info_label = QLabel("لم يتم اختيار ملف")
        layout.addWidget(self.info_label)
        
        # مشغل الفيديو
        self.video_widget = QVideoWidget()
        layout.addWidget(self.video_widget)
        
        # مشغل الوسائط
        self.player = QMediaPlayer()
        self.player.setVideoOutput(self.video_widget)
        
        # ربط الإشارات
        self.player.mediaStatusChanged.connect(self.on_media_status_changed)
        self.player.error.connect(self.on_error)
        
        # أزرار التحكم
        self.play_btn = QPushButton("تشغيل")
        self.play_btn.clicked.connect(self.toggle_play)
        self.play_btn.setEnabled(False)
        layout.addWidget(self.play_btn)
        
        self.test_copy_btn = QPushButton("اختبار مع نسخة آمنة")
        self.test_copy_btn.clicked.connect(self.test_with_safe_copy)
        self.test_copy_btn.setEnabled(False)
        layout.addWidget(self.test_copy_btn)
        
        self.setLayout(layout)
        
    def select_file(self):
        """اختيار ملف الفيديو"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختر ملف فيديو", "", 
            "Video Files (*.mp4 *.avi *.mkv *.mov *.wmv);;All Files (*)"
        )
        
        if file_path:
            self.current_file = file_path
            self.info_label.setText(f"الملف: {os.path.basename(file_path)}")
            print(f"🎬 تم اختيار الملف: {file_path}")
            self.load_video(file_path)
            self.test_copy_btn.setEnabled(True)
    
    def create_safe_copy(self, file_path):
        """إنشاء نسخة آمنة بمسار بسيط"""
        try:
            # تنظيف النسخة المؤقتة السابقة
            if self.temp_file and os.path.exists(self.temp_file):
                try:
                    os.remove(self.temp_file)
                except:
                    pass
            
            print(f"🔄 إنشاء نسخة آمنة...")
            print(f"📁 المسار الأصلي: {file_path}")
            
            # إنشاء مجلد مؤقت
            temp_dir = tempfile.mkdtemp(prefix="video_safe_")
            
            # الحصول على امتداد الملف
            file_ext = os.path.splitext(file_path)[1]
            
            # إنشاء اسم ملف آمن
            safe_filename = f"safe_video{file_ext}"
            safe_path = os.path.join(temp_dir, safe_filename)
            
            print(f"📁 المسار الآمن: {safe_path}")
            
            # نسخ الملف
            print("📋 جاري النسخ...")
            shutil.copy2(file_path, safe_path)
            
            if os.path.exists(safe_path):
                file_size = os.path.getsize(safe_path) / (1024 * 1024)
                print(f"✅ تم إنشاء النسخة الآمنة ({file_size:.1f} MB)")
                self.temp_file = safe_path
                return safe_path
            else:
                print("❌ فشل في إنشاء النسخة الآمنة")
                return None
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء النسخة الآمنة: {str(e)}")
            return None
    
    def load_video(self, file_path):
        """تحميل الفيديو"""
        try:
            print(f"🔄 تحميل الفيديو: {file_path}")
            
            # فحص المسار
            path_issues = []
            if any(ord(char) > 127 for char in file_path):
                path_issues.append("أحرف عربية")
            if '[' in file_path or ']' in file_path:
                path_issues.append("أقواس مربعة")
            if len(file_path) > 200:
                path_issues.append("مسار طويل")
            
            if path_issues:
                print(f"⚠️ مشاكل في المسار: {', '.join(path_issues)}")
            
            # تحويل إلى مسار مطلق
            abs_path = os.path.abspath(file_path)
            print(f"📁 المسار المطلق: {abs_path}")
            
            # إنشاء URL
            file_url = QUrl.fromLocalFile(abs_path)
            print(f"🔗 URL: {file_url.toString()}")
            
            if not file_url.isValid():
                print("❌ URL غير صالح")
                return False
            
            # تحميل الوسائط
            media_content = QMediaContent(file_url)
            self.player.setMedia(media_content)
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تحميل الفيديو: {str(e)}")
            return False
    
    def test_with_safe_copy(self):
        """اختبار مع نسخة آمنة"""
        if hasattr(self, 'current_file'):
            safe_path = self.create_safe_copy(self.current_file)
            if safe_path:
                print("✅ تم إنشاء النسخة الآمنة")
                self.info_label.setText(f"اختبار مع نسخة آمنة: {os.path.basename(safe_path)}")
                self.load_video(safe_path)
            else:
                print("❌ فشل في إنشاء النسخة الآمنة")
    
    def toggle_play(self):
        """تبديل التشغيل"""
        if self.player.state() == QMediaPlayer.PlayingState:
            self.player.pause()
            self.play_btn.setText("تشغيل")
        else:
            self.player.play()
            self.play_btn.setText("إيقاف")
    
    def on_media_status_changed(self, status):
        """معالجة تغيير حالة الوسائط"""
        status_names = {
            QMediaPlayer.NoMedia: "لا توجد وسائط",
            QMediaPlayer.LoadingMedia: "جاري التحميل",
            QMediaPlayer.LoadedMedia: "تم التحميل",
            QMediaPlayer.StalledMedia: "متوقف",
            QMediaPlayer.BufferingMedia: "جاري التخزين المؤقت",
            QMediaPlayer.BufferedMedia: "جاهز للتشغيل",
            QMediaPlayer.EndOfMedia: "انتهى الملف",
            QMediaPlayer.InvalidMedia: "ملف غير صالح"
        }
        
        status_name = status_names.get(status, f"غير معروف ({status})")
        print(f"📊 حالة الوسائط: {status_name}")
        
        if status == QMediaPlayer.LoadedMedia:
            self.play_btn.setEnabled(True)
            self.info_label.setText(self.info_label.text() + " - ✅ تم التحميل بنجاح")
            print("🎉 نجح تحميل الفيديو!")
        elif status == QMediaPlayer.InvalidMedia:
            self.play_btn.setEnabled(False)
            self.info_label.setText(self.info_label.text() + " - ❌ ملف غير صالح")
            print("❌ فشل تحميل الفيديو!")
    
    def on_error(self, error):
        """معالجة الأخطاء"""
        error_names = {
            QMediaPlayer.NoError: "لا توجد أخطاء",
            QMediaPlayer.ResourceError: "خطأ في المورد",
            QMediaPlayer.FormatError: "خطأ في التنسيق",
            QMediaPlayer.NetworkError: "خطأ في الشبكة",
            QMediaPlayer.AccessDeniedError: "رفض الوصول"
        }
        
        error_name = error_names.get(error, f"خطأ غير معروف ({error})")
        print(f"❌ خطأ في مشغل الوسائط: {error_name}")
        
        if error != QMediaPlayer.NoError:
            self.info_label.setText(self.info_label.text() + f" - ❌ {error_name}")
    
    def closeEvent(self, event):
        """تنظيف عند الإغلاق"""
        # تنظيف الملف المؤقت
        if self.temp_file and os.path.exists(self.temp_file):
            try:
                os.remove(self.temp_file)
                print(f"🗑️ تم حذف الملف المؤقت: {self.temp_file}")
            except:
                pass
        
        event.accept()

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تعيين اتجاه النص
    app.setLayoutDirection(app.RightToLeft)
    
    window = VideoTester()
    window.show()
    
    print("🎬 أداة اختبار الفيديو جاهزة!")
    print("📋 الخطوات:")
    print("1. اضغط 'اختر ملف فيديو'")
    print("2. اختر الفيديو من المسار المعقد")
    print("3. شوف الرسائل هنا")
    print("4. جرب 'اختبار مع نسخة آمنة' لو مشتغلش")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
