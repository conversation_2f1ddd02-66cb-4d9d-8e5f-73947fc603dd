#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مشغل فيديو بديل باستخدام OpenCV
"""

import os
import cv2
import numpy as np
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QSlider, QLabel, QFrame)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QImage, QPixmap

class VideoThread(QThread):
    """خيط لتشغيل الفيديو"""
    frameReady = pyqtSignal(np.ndarray)
    positionChanged = pyqtSignal(int)
    durationChanged = pyqtSignal(int)

    def __init__(self):
        super().__init__()
        self.cap = None
        self.playing = False
        self.current_frame = 0
        self.total_frames = 0
        self.fps = 30

    def load_video(self, file_path):
        """تحميل الفيديو"""
        try:
            if self.cap:
                self.cap.release()

            self.cap = cv2.VideoCapture(file_path)

            if not self.cap.isOpened():
                print(f"❌ فشل في فتح الفيديو: {file_path}")
                return False

            self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.fps = self.cap.get(cv2.CAP_PROP_FPS)

            if self.fps <= 0:
                self.fps = 30  # قيمة افتراضية

            duration_seconds = self.total_frames / self.fps
            self.durationChanged.emit(int(duration_seconds * 1000))  # بالميلي ثانية

            print(f"✅ تم تحميل الفيديو: {self.total_frames} إطار، {self.fps} fps")
            return True

        except Exception as e:
            print(f"❌ خطأ في تحميل الفيديو: {str(e)}")
            return False

    def play(self):
        """تشغيل الفيديو"""
        self.playing = True

    def pause(self):
        """إيقاف الفيديو"""
        self.playing = False

    def seek(self, position_ms):
        """الانتقال لموضع معين"""
        if self.cap and self.total_frames > 0:
            frame_number = int((position_ms / 1000.0) * self.fps)
            frame_number = max(0, min(frame_number, self.total_frames - 1))

            self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            self.current_frame = frame_number

    def run(self):
        """تشغيل الخيط"""
        if not self.cap:
            return

        frame_delay = 1000 / self.fps  # بالميلي ثانية

        while self.cap and self.cap.isOpened():
            if self.playing:
                ret, frame = self.cap.read()

                if ret:
                    self.frameReady.emit(frame)
                    self.current_frame += 1

                    # إرسال موضع التشغيل
                    position_ms = int((self.current_frame / self.fps) * 1000)
                    self.positionChanged.emit(position_ms)

                    # تأخير للحفاظ على معدل الإطارات
                    self.msleep(int(frame_delay))
                else:
                    # انتهى الفيديو
                    self.playing = False
                    self.current_frame = 0
                    self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
            else:
                self.msleep(50)  # انتظار قصير عند الإيقاف

    def stop(self):
        """إيقاف الخيط"""
        self.playing = False
        if self.cap:
            self.cap.release()
        self.quit()
        self.wait()

class OpenCVVideoPlayer(QWidget):
    """مشغل فيديو باستخدام OpenCV"""

    media_loaded = pyqtSignal(bool)
    playback_state_changed = pyqtSignal(bool)

    def __init__(self, config=None):
        """تهيئة مشغل الفيديو"""
        super().__init__()

        self.config = config
        self.current_file = None
        self.is_playing = False
        self.duration = 0

        self._init_ui()
        self._init_video_thread()

    def _init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout()

        # منطقة عرض الفيديو
        self.video_label = QLabel()
        self.video_label.setMinimumSize(640, 480)
        self.video_label.setStyleSheet("background-color: black; border: 1px solid gray;")
        self.video_label.setAlignment(Qt.AlignCenter)
        self.video_label.setText("لا يوجد فيديو")
        layout.addWidget(self.video_label)

        # أزرار التحكم
        controls_layout = QHBoxLayout()

        self.play_button = QPushButton("▶")
        self.play_button.setMaximumWidth(50)
        self.play_button.clicked.connect(self.toggle_play)
        self.play_button.setEnabled(False)
        controls_layout.addWidget(self.play_button)

        # شريط التقدم
        self.position_slider = QSlider(Qt.Horizontal)
        self.position_slider.setEnabled(False)
        self.position_slider.sliderMoved.connect(self.set_position)
        controls_layout.addWidget(self.position_slider)

        # تسمية الوقت
        self.time_label = QLabel("00:00 / 00:00")
        self.time_label.setMinimumWidth(100)
        controls_layout.addWidget(self.time_label)

        layout.addLayout(controls_layout)

        self.setLayout(layout)

    def _init_video_thread(self):
        """تهيئة خيط الفيديو"""
        self.video_thread = VideoThread()
        self.video_thread.frameReady.connect(self.update_frame)
        self.video_thread.positionChanged.connect(self.update_position)
        self.video_thread.durationChanged.connect(self.update_duration)

    def load_media(self, file_path):
        """تحميل ملف فيديو"""
        print(f"🔄 تحميل الفيديو باستخدام OpenCV: {file_path}")

        if not os.path.exists(file_path):
            print(f"❌ الملف غير موجود: {file_path}")
            self.media_loaded.emit(False)
            return

        # التحقق من امتداد الملف
        supported_formats = ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v']
        file_ext = os.path.splitext(file_path)[1].lower()

        if file_ext not in supported_formats:
            print(f"❌ تنسيق غير مدعوم: {file_ext}")
            self.media_loaded.emit(False)
            return

        # إيقاف الخيط السابق
        if hasattr(self, 'video_thread') and self.video_thread.isRunning():
            self.video_thread.stop()

        # تحميل الفيديو الجديد
        success = self.video_thread.load_video(file_path)

        if success:
            self.current_file = file_path
            self.play_button.setEnabled(True)
            self.position_slider.setEnabled(True)

            # بدء خيط الفيديو
            self.video_thread.start()

            print(f"✅ تم تحميل الفيديو بنجاح")
            self.media_loaded.emit(True)
        else:
            print(f"❌ فشل في تحميل الفيديو")
            self.media_loaded.emit(False)

    def update_frame(self, frame):
        """تحديث إطار الفيديو"""
        try:
            # تحويل من BGR إلى RGB
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # تحويل إلى QImage
            h, w, ch = rgb_frame.shape
            bytes_per_line = ch * w
            qt_image = QImage(rgb_frame.data, w, h, bytes_per_line, QImage.Format_RGB888)

            # تحجيم الصورة لتناسب العرض
            label_size = self.video_label.size()
            scaled_pixmap = QPixmap.fromImage(qt_image).scaled(
                label_size, Qt.KeepAspectRatio, Qt.SmoothTransformation
            )

            self.video_label.setPixmap(scaled_pixmap)

        except Exception as e:
            print(f"خطأ في عرض الإطار: {str(e)}")

    def update_position(self, position_ms):
        """تحديث موضع التشغيل"""
        if self.duration > 0:
            self.position_slider.setValue(int((position_ms / self.duration) * 100))

        # تحديث تسمية الوقت
        current_time = self._format_time(position_ms)
        total_time = self._format_time(self.duration)
        self.time_label.setText(f"{current_time} / {total_time}")

    def update_duration(self, duration_ms):
        """تحديث مدة الفيديو"""
        self.duration = duration_ms
        self.position_slider.setMaximum(100)

    def toggle_play(self):
        """تبديل حالة التشغيل"""
        if not self.current_file:
            return

        if self.is_playing:
            self.video_thread.pause()
            self.play_button.setText("▶")
            self.is_playing = False
        else:
            self.video_thread.play()
            self.play_button.setText("⏸")
            self.is_playing = True

        self.playback_state_changed.emit(self.is_playing)

    def set_position(self, position):
        """تعيين موضع التشغيل"""
        if self.duration > 0:
            position_ms = int((position / 100.0) * self.duration)
            self.video_thread.seek(position_ms)

    def stop(self):
        """إيقاف التشغيل"""
        if hasattr(self, 'video_thread'):
            self.video_thread.pause()
            self.video_thread.seek(0)  # العودة للبداية
            self.play_button.setText("▶")
            self.is_playing = False
            self.playback_state_changed.emit(False)

    def toggle_mute(self):
        """تبديل كتم الصوت (غير مدعوم في OpenCV)"""
        print("⚠️ كتم الصوت غير مدعوم في مشغل OpenCV")
        pass

    def set_volume(self, volume):
        """تعيين مستوى الصوت (غير مدعوم في OpenCV)"""
        print(f"⚠️ تعيين مستوى الصوت غير مدعوم في مشغل OpenCV: {volume}")
        pass

    def toggle_fullscreen(self):
        """تبديل وضع ملء الشاشة"""
        if self.isFullScreen():
            self.showNormal()
            print("🔄 الخروج من وضع ملء الشاشة")
        else:
            self.showFullScreen()
            print("🔄 الدخول في وضع ملء الشاشة")

    def get_duration(self):
        """الحصول على مدة الفيديو"""
        return self.duration

    def get_position(self):
        """الحصول على الموضع الحالي"""
        if hasattr(self, 'video_thread'):
            return int((self.video_thread.current_frame / self.video_thread.fps) * 1000)
        return 0

    def _format_time(self, milliseconds):
        """تنسيق الوقت"""
        seconds = int(milliseconds / 1000)
        minutes = seconds // 60
        seconds %= 60
        return f"{minutes:02d}:{seconds:02d}"

    def closeEvent(self, event):
        """تنظيف عند الإغلاق"""
        if hasattr(self, 'video_thread'):
            self.video_thread.stop()
        event.accept()
