#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة معالجة الوسائط المبسطة
"""

import os
import time
import whisper
import pydub
from pydub import AudioSegment

class SimpleMediaProcessor:
    """فئة معالجة الوسائط"""

    def __init__(self, config):
        """تهيئة معالج الوسائط"""
        self.config = config
        self.whisper_model = None

    def check_whisper_model(self, model_name):
        """التحقق من وجود نموذج Whisper مع تفاصيل واضحة"""
        try:
            # التحقق من وجود النموذج في مجلد models المحلي أولاً
            models_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "models", "whisper")
            model_file = os.path.join(models_dir, f"{model_name}.pt")

            if os.path.exists(model_file):
                file_size = os.path.getsize(model_file) / (1024 * 1024)  # بالميجابايت
                print(f"✅ نموذج Whisper '{model_name}' موجود محلياً ({file_size:.1f} MB)")
                return True

            # التحقق من وجود النموذج في مجلد cache
            whisper_dir = os.path.join(os.path.expanduser("~"), ".cache", "whisper")
            cache_model_file = os.path.join(whisper_dir, model_name + ".pt")

            if os.path.exists(cache_model_file):
                file_size = os.path.getsize(cache_model_file) / (1024 * 1024)  # بالميجابايت
                print(f"✅ نموذج Whisper '{model_name}' موجود في cache ({file_size:.1f} MB)")
                return True

            print(f"❌ نموذج Whisper '{model_name}' غير موجود")
            print(f"   المسارات المفحوصة:")
            print(f"   - {model_file}")
            print(f"   - {cache_model_file}")
            return False

        except Exception as e:
            print(f"خطأ في التحقق من نموذج Whisper: {str(e)}")
            return False

    def check_all_models(self):
        """التحقق من جميع النماذج المطلوبة"""
        print("🔍 فحص النماذج المطلوبة...")
        print("=" * 50)

        missing_models = []

        # 1. فحص نموذج Whisper
        whisper_model = self.config.get("translation", "whisper_model", "medium")
        print(f"📝 فحص نموذج Whisper '{whisper_model}':")
        if not self.check_whisper_model(whisper_model):
            missing_models.append(f"Whisper {whisper_model}")
        print()

        # 2. فحص نماذج الترجمة
        print("🌐 فحص نماذج الترجمة:")
        translation_models = [
            ("en-ar", "Helsinki-NLP/opus-mt-en-ar"),
            ("ar-en", "Helsinki-NLP/opus-mt-ar-en"),
            ("en-fr", "Helsinki-NLP/opus-mt-en-fr")
        ]

        for model_key, model_name in translation_models:
            models_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "models", "translation")
            model_path = os.path.join(models_dir, model_key)

            if os.path.exists(model_path) and any(os.listdir(model_path)):
                # حساب حجم النموذج
                total_size = sum(os.path.getsize(os.path.join(model_path, f))
                               for f in os.listdir(model_path) if os.path.isfile(os.path.join(model_path, f)))
                size_mb = total_size / (1024 * 1024)
                print(f"   ✅ نموذج {model_key}: موجود ({size_mb:.1f} MB)")
            else:
                print(f"   ❌ نموذج {model_key}: غير موجود")
                missing_models.append(f"Translation {model_key}")
        print()

        # 3. فحص نماذج TTS
        print("🔊 فحص نماذج TTS:")
        try:
            from TTS.api import TTS
            print("   ✅ مكتبة TTS متاحة")

            # فحص النماذج المحلية
            tts_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "models", "tts")
            if os.path.exists(tts_dir) and os.listdir(tts_dir):
                print(f"   ✅ نماذج TTS محلية موجودة")
            else:
                print(f"   ⚠️ نماذج TTS محلية غير موجودة (سيتم التنزيل عند الحاجة)")
        except ImportError:
            print("   ⚠️ مكتبة TTS غير مثبتة (سيتم استخدام gTTS)")
        print()

        # النتيجة النهائية
        print("=" * 50)
        if missing_models:
            print(f"❌ النماذج المفقودة ({len(missing_models)}):")
            for model in missing_models:
                print(f"   - {model}")
            print()
            print("💡 لتنزيل النماذج المفقودة:")
            print("   - شغل download_models.bat لتنزيل جميع النماذج")
            print("   - أو استخدم الوظائف في البرنامج وسيتم التنزيل تلقائياً")
            return False
        else:
            print("🎉 جميع النماذج موجودة ومتاحة!")
            return True

    def download_whisper_model(self, model_name, progress_callback=None):
        """تنزيل نموذج Whisper"""
        try:
            # تنزيل النموذج باستخدام مكتبة Whisper
            if progress_callback:
                progress_callback(0, f"جاري تنزيل نموذج Whisper {model_name}...")

            # تنزيل النموذج
            whisper.load_model(model_name)

            if progress_callback:
                progress_callback(100, f"تم تنزيل نموذج Whisper {model_name} بنجاح")

            return True
        except Exception as e:
            if progress_callback:
                progress_callback(0, f"فشل في تنزيل نموذج Whisper: {str(e)}")
            return False

    def extract_audio(self, video_path, output_path=None, progress_callback=None):
        """استخراج الصوت من الفيديو"""
        try:
            if progress_callback:
                progress_callback(0, "جاري استخراج الصوت من الفيديو...")

            # تحديد مسار الإخراج
            if output_path is None:
                temp_dir = self.config.get("paths", "temp_dir")
                output_path = os.path.join(temp_dir, f"audio_{int(time.time())}.wav")

            # استخدام ffmpeg مباشرة عبر pydub
            import subprocess
            command = [
                "ffmpeg", "-i", video_path,
                "-vn", "-acodec", "pcm_s16le",
                "-ar", "44100", "-ac", "2",
                output_path
            ]

            subprocess.run(command, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

            if progress_callback:
                progress_callback(100, "تم استخراج الصوت بنجاح")

            return output_path
        except Exception as e:
            if progress_callback:
                progress_callback(0, f"فشل في استخراج الصوت: {str(e)}")
            return None

    def transcribe_audio(self, audio_path, model=None, language=None, progress_callback=None):
        """استخراج النص من الصوت باستخدام نموذج Whisper المحلي"""
        try:
            if progress_callback:
                progress_callback(0, "جاري استخراج النص من الصوت...")

            # تحديد النموذج
            if model is None:
                model = self.config.get("translation", "whisper_model", "medium")

            # التحقق من وجود النموذج في مجلد models المحلي أولاً
            models_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "models", "whisper")
            os.makedirs(models_dir, exist_ok=True)
            model_file = os.path.join(models_dir, f"{model}.pt")

            # إذا لم يوجد في مجلد models، تحقق من مجلد cache ونسخه
            if not os.path.exists(model_file):
                whisper_dir = os.path.join(os.path.expanduser("~"), ".cache", "whisper")
                cache_model_file = os.path.join(whisper_dir, model + ".pt")

                if os.path.exists(cache_model_file):
                    # نسخ النموذج من cache إلى مجلد models المحلي
                    import shutil
                    if progress_callback:
                        progress_callback(5, "جاري نسخ النموذج إلى المجلد المحلي...")
                    shutil.copy2(cache_model_file, model_file)
                    if progress_callback:
                        progress_callback(10, "تم نسخ النموذج إلى المجلد المحلي")
                else:
                    model_file = cache_model_file  # استخدام مسار cache للتنزيل

            if not os.path.exists(model_file):
                if progress_callback:
                    progress_callback(0, f"خطأ: لم يتم العثور على نموذج Whisper '{model}'")
                    progress_callback(0, f"المسار المتوقع: {model_file}")
                    progress_callback(0, "يرجى تشغيل ملف setup_whisper.bat لإعداد النموذج")
                return None

            # التحقق من حجم النموذج
            file_size = os.path.getsize(model_file)
            expected_size = 1.5 * 1024 * 1024 * 1024  # 1.5 جيجابايت للنموذج المتوسط

            if file_size < 0.8 * expected_size:  # أقل من 80% من الحجم المتوقع
                if progress_callback:
                    progress_callback(0, f"خطأ: نموذج Whisper غير مكتمل")
                    progress_callback(0, f"الحجم الحالي: {file_size / (1024*1024):.2f} ميجابايت")
                    progress_callback(0, f"الحجم المتوقع: {expected_size / (1024*1024):.2f} ميجابايت")
                    progress_callback(0, "يرجى تشغيل ملف setup_whisper.bat لإعداد النموذج")
                return None

            # تحميل النموذج إذا لم يكن محملاً بالفعل أو إذا تغير النموذج
            if self.whisper_model is None or getattr(self.whisper_model, 'name', '') != model:
                if progress_callback:
                    progress_callback(10, f"جاري تحميل نموذج Whisper {model} من الملف المحلي...")

                # تحميل النموذج محلياً بدون تنزيل
                import whisper

                # تعطيل التنزيل التلقائي
                os.environ['WHISPER_CACHE_DIR'] = os.path.dirname(model_file)

                try:
                    self.whisper_model = whisper.load_model(model, download_root=os.path.dirname(model_file))
                    self.whisper_model.name = model  # حفظ اسم النموذج للمقارنة
                except Exception as e:
                    if progress_callback:
                        progress_callback(0, f"فشل في تحميل النموذج: {str(e)}")
                    return None

                if progress_callback:
                    progress_callback(20, f"تم تحميل نموذج Whisper {model} بنجاح")

            # تحديد خيارات الاستخراج مع الكشف التلقائي للغة
            options = {
                "verbose": False,
                "task": "transcribe"
            }

            # إضافة اللغة إذا تم تحديدها
            if language is not None and language != "auto":
                options["language"] = language
            else:
                # تمكين الكشف التلقائي للغة
                if progress_callback:
                    progress_callback(25, "جاري الكشف التلقائي عن اللغة...")

            # استخراج النص مع عرض التقدم
            if progress_callback:
                progress_callback(30, "جاري استخراج النص...")

            # استخدام النموذج لاستخراج النص
            result = self.whisper_model.transcribe(
                audio_path,
                **options,
                # إضافة callback للتقدم إذا أمكن
                progress_callback=lambda x: progress_callback(30 + int(x * 0.6), "جاري استخراج النص...") if progress_callback else None
            )

            if progress_callback:
                progress_callback(95, "جاري معالجة النتائج...")

            # التحقق من وجود النص في النتيجة
            if result and "text" in result and result["text"].strip():
                # إضافة معلومات إضافية عن اللغة المكتشفة
                detected_language = result.get("language", "غير محدد")
                if progress_callback:
                    progress_callback(100, f"تم استخراج النص بنجاح - اللغة المكتشفة: {detected_language}")

                # إضافة معلومات اللغة المكتشفة إلى النتيجة
                result["detected_language"] = detected_language
                return result
            else:
                if progress_callback:
                    progress_callback(0, "لم يتم العثور على نص في الملف الصوتي")
                return None

        except Exception as e:
            if progress_callback:
                progress_callback(0, f"فشل في استخراج النص: {str(e)}")
            return None

    def dub_video(self, video_path, audio_path, output_path=None, mute_original=False,
                 lower_original=True, quality="high", progress_callback=None):
        """دبلجة الفيديو"""
        try:
            if progress_callback:
                progress_callback(0, "جاري تحضير الدبلجة...")

            # تحديد مسار الإخراج
            if output_path is None:
                output_dir = self.config.get("paths", "output_dir")
                video_name = os.path.splitext(os.path.basename(video_path))[0]
                output_path = os.path.join(output_dir, f"{video_name}_dubbed.mp4")

            # استخراج الصوت الأصلي من الفيديو
            if progress_callback:
                progress_callback(20, "جاري استخراج الصوت الأصلي...")

            temp_dir = self.config.get("paths", "temp_dir")
            original_audio_path = os.path.join(temp_dir, f"original_audio_{int(time.time())}.wav")

            # استخراج الصوت الأصلي
            import subprocess
            extract_cmd = [
                "ffmpeg", "-i", video_path,
                "-vn", "-acodec", "pcm_s16le",
                "-ar", "44100", "-ac", "2",
                original_audio_path
            ]

            subprocess.run(extract_cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

            # تحميل الصوت الأصلي والصوت المدبلج
            if progress_callback:
                progress_callback(40, "جاري معالجة الصوت...")

            original_audio = AudioSegment.from_file(original_audio_path)
            dubbed_audio = AudioSegment.from_file(audio_path)

            # إعداد الصوت النهائي
            if mute_original:
                # كتم الصوت الأصلي
                final_audio = dubbed_audio
            else:
                # دمج الصوت الأصلي مع الصوت المدبلج
                if lower_original:
                    # خفض مستوى الصوت الأصلي
                    original_audio = original_audio - 10  # خفض بمقدار 10 ديسيبل

                # التأكد من أن الصوت المدبلج بنفس طول الصوت الأصلي
                if len(dubbed_audio) < len(original_audio):
                    # إضافة صمت في نهاية الصوت المدبلج
                    silence = AudioSegment.silent(duration=len(original_audio) - len(dubbed_audio))
                    dubbed_audio = dubbed_audio + silence

                # دمج الصوت
                final_audio = original_audio.overlay(dubbed_audio)

            # حفظ الصوت النهائي
            final_audio_path = os.path.join(temp_dir, f"final_audio_{int(time.time())}.wav")
            final_audio.export(final_audio_path, format="wav")

            # دمج الصوت مع الفيديو
            if progress_callback:
                progress_callback(70, "جاري دمج الصوت مع الفيديو...")

            # تحديد جودة الفيديو
            if quality == "high":
                bitrate = "8000k"
            elif quality == "medium":
                bitrate = "4000k"
            else:
                bitrate = "2000k"

            # دمج الصوت مع الفيديو
            merge_cmd = [
                "ffmpeg", "-i", video_path,
                "-i", final_audio_path,
                "-c:v", "copy",
                "-c:a", "aac",
                "-b:a", "192k",
                "-b:v", bitrate,
                "-strict", "experimental",
                "-map", "0:v:0",
                "-map", "1:a:0",
                output_path
            ]

            subprocess.run(merge_cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

            # تنظيف الملفات المؤقتة
            try:
                os.remove(original_audio_path)
                os.remove(final_audio_path)
            except:
                pass

            if progress_callback:
                progress_callback(100, "تم دبلجة الفيديو بنجاح")

            return {"output_path": output_path}
        except Exception as e:
            if progress_callback:
                progress_callback(0, f"فشل في دبلجة الفيديو: {str(e)}")
            return None
