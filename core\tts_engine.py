#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
محرك تحويل النص إلى كلام
"""

import os
import time
import tempfile
from gtts import gTTS

class TTSEngine:
    """فئة محرك تحويل النص إلى كلام"""

    def __init__(self, config):
        """تهيئة محرك تحويل النص إلى كلام"""
        self.config = config
        self.tts_model = None
        self.current_model_name = None

        # مجلد النماذج المحلي
        self.models_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "models", "tts")
        os.makedirs(self.models_dir, exist_ok=True)

        # محاولة تحميل Coqui TTS
        self.coqui_available = self._check_coqui_availability()

    def check_tts_model(self, model_name):
        """التحقق من وجود نموذج TTS"""
        # gTTS لا تتطلب تنزيل نماذج مسبقًا
        return True

    def download_tts_model(self, model_name, progress_callback=None):
        """تنزيل نموذج TTS"""
        # gTTS لا تتطلب تنزيل نماذج مسبقًا
        if progress_callback:
            progress_callback(100, "لا حاجة لتنزيل نماذج لـ gTTS")
        return True

    def generate_speech(self, text, model=None, language=None, gender="male", speed=1.0,
                       enhance=True, is_sample=False, progress_callback=None):
        """توليد الكلام من النص"""
        try:
            if progress_callback:
                progress_callback(0, "جاري تحضير توليد الصوت...")

            # تحديد اللغة
            if language is None or language == "auto":
                # استخدام العربية كلغة افتراضية
                language = "ar"

            # تحويل رموز اللغة إلى الصيغة المدعومة في gTTS
            lang_map = {
                "ar": "ar",
                "en": "en",
                "fr": "fr",
                "es": "es",
                "de": "de",
                "it": "it",
                "ru": "ru",
                "zh": "zh-CN",
                "ja": "ja"
            }

            tts_lang = lang_map.get(language, "ar")

            if progress_callback:
                progress_callback(30, "جاري توليد الصوت...")

            # تحديد مسار الإخراج
            temp_dir = self.config.get("paths", "temp_dir")
            file_prefix = "sample" if is_sample else "speech"
            output_path = os.path.join(temp_dir, f"{file_prefix}_{int(time.time())}.mp3")

            # محاولة استخدام Coqui TTS أولاً
            wav_output_path = output_path.replace(".mp3", ".wav")

            if self.coqui_available:
                if progress_callback:
                    progress_callback(40, "جاري استخدام Coqui TTS...")

                if self._generate_with_coqui(text, language, wav_output_path, progress_callback):
                    print("✅ تم توليد الصوت باستخدام Coqui TTS")
                else:
                    print("⚠️ فشل Coqui TTS، جاري التبديل إلى gTTS...")
                    # العودة إلى gTTS
                    tts = gTTS(text=text, lang=tts_lang, slow=(speed < 1.0))
                    tts.save(output_path)

                    if progress_callback:
                        progress_callback(70, "جاري معالجة الصوت...")

                    # تحويل الملف إلى صيغة WAV
                    self._convert_to_wav(output_path, wav_output_path)
            else:
                # استخدام gTTS مباشرة
                if progress_callback:
                    progress_callback(40, "جاري استخدام gTTS...")

                tts = gTTS(text=text, lang=tts_lang, slow=(speed < 1.0))
                tts.save(output_path)

                if progress_callback:
                    progress_callback(70, "جاري معالجة الصوت...")

                # تحويل الملف إلى صيغة WAV
                self._convert_to_wav(output_path, wav_output_path)

            # تعديل سرعة الصوت إذا لزم الأمر (للسرعات غير المدعومة في gTTS)
            if speed != 1.0 and speed != 0.5:
                if progress_callback:
                    progress_callback(80, "جاري تعديل سرعة الصوت...")

                self._adjust_speech_rate(wav_output_path, speed)

            # تحسين جودة الصوت إذا لزم الأمر
            if enhance:
                if progress_callback:
                    progress_callback(90, "جاري تحسين جودة الصوت...")

                self._enhance_audio(wav_output_path)

            if progress_callback:
                progress_callback(100, "تم توليد الصوت بنجاح")

            # حذف ملف MP3 الأصلي
            try:
                os.remove(output_path)
            except:
                pass

            return {"audio_path": wav_output_path}
        except Exception as e:
            if progress_callback:
                progress_callback(0, f"فشل في توليد الصوت: {str(e)}")
            return None

    def _convert_to_wav(self, mp3_path, wav_path):
        """تحويل ملف MP3 إلى WAV"""
        try:
            from pydub import AudioSegment

            # تحميل الصوت
            sound = AudioSegment.from_mp3(mp3_path)

            # حفظ الصوت بصيغة WAV
            sound.export(wav_path, format="wav")

            return True
        except Exception as e:
            print(f"فشل في تحويل الصوت إلى WAV: {str(e)}")
            return False

    def _adjust_speech_rate(self, audio_path, speed):
        """تعديل سرعة الصوت"""
        try:
            from pydub import AudioSegment

            # تحميل الصوت
            sound = AudioSegment.from_file(audio_path)

            # تعديل السرعة
            # نستخدم معدل العينات لتغيير السرعة
            new_sample_rate = int(sound.frame_rate * (1.0 / speed))

            # تصدير الصوت بالسرعة الجديدة
            sound = sound._spawn(sound.raw_data, overrides={
                "frame_rate": new_sample_rate
            })
            sound = sound.set_frame_rate(44100)  # إعادة تعيين معدل العينات إلى القيمة القياسية

            # حفظ الصوت
            sound.export(audio_path, format="wav")

            return True
        except Exception as e:
            print(f"فشل في تعديل سرعة الصوت: {str(e)}")
            return False

    def _enhance_audio(self, audio_path):
        """تحسين جودة الصوت"""
        try:
            from pydub import AudioSegment
            from pydub.effects import normalize

            # تحميل الصوت
            sound = AudioSegment.from_file(audio_path)

            # تطبيق التحسينات
            sound = normalize(sound)  # تطبيع مستوى الصوت

            # تطبيق مرشح تمرير منخفض لإزالة الضوضاء العالية
            # هذا يتطلب مكتبات إضافية مثل scipy

            # حفظ الصوت
            sound.export(audio_path, format="wav")

            return True
        except Exception as e:
            print(f"فشل في تحسين جودة الصوت: {str(e)}")
            return False

    def _check_coqui_availability(self):
        """التحقق من توفر Coqui TTS"""
        try:
            from TTS.api import TTS
            return True
        except ImportError:
            return False

    def _load_coqui_model(self, language="ar", progress_callback=None):
        """تحميل نموذج Coqui TTS"""
        if not self.coqui_available:
            return None

        try:
            from TTS.api import TTS

            # تحديد النموذج المناسب
            if language == "ar":
                model_name = "tts_models/ar/fairseq/tts-transformer"
            elif language == "en":
                model_name = "tts_models/en/ljspeech/tacotron2-DDC"
            else:
                model_name = "tts_models/multilingual/multi-dataset/your_tts"

            if progress_callback:
                progress_callback(20, f"جاري تحميل نموذج Coqui TTS: {model_name}")

            # تحميل النموذج
            tts = TTS(model_name=model_name, progress_bar=False)

            if progress_callback:
                progress_callback(50, "تم تحميل نموذج Coqui TTS بنجاح")

            return tts

        except Exception as e:
            if progress_callback:
                progress_callback(0, f"فشل في تحميل Coqui TTS: {str(e)}")
            return None

    def _generate_with_coqui(self, text, language, output_path, progress_callback=None):
        """توليد الكلام باستخدام Coqui TTS"""
        try:
            # تحميل النموذج
            tts_model = self._load_coqui_model(language, progress_callback)
            if tts_model is None:
                return False

            if progress_callback:
                progress_callback(60, "جاري توليد الكلام باستخدام Coqui TTS...")

            # توليد الصوت
            tts_model.tts_to_file(text=text, file_path=output_path)

            if progress_callback:
                progress_callback(80, "تم توليد الكلام باستخدام Coqui TTS")

            return True

        except Exception as e:
            if progress_callback:
                progress_callback(0, f"فشل في توليد الكلام باستخدام Coqui TTS: {str(e)}")
            return False
