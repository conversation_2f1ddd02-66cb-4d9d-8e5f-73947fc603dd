@echo off
title اختبار تشغيل الفيديو

echo ============================================================
echo اختبار تشغيل الفيديو
echo ============================================================
echo.

REM التحقق من وجود Python
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ❌ خطأ: لم يتم العثور على Python
    echo.
    pause
    exit /b 1
)

echo ✅ Python متاح
echo.

echo 🔄 تشغيل أداة اختبار الفيديو...
echo.
echo 💡 هذه الأداة ستساعدك في:
echo    • اختبار تشغيل ملفات الفيديو
echo    • تجربة النسخ المؤقتة للملفات ذات المسارات العربية
echo    • تشخيص مشاكل التشغيل
echo.

python test_video_simple.py

echo.
echo تم إغلاق أداة الاختبار
pause
