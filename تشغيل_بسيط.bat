@echo off
title مشغل الوسائط - نسخة بسيطة

echo ============================================================
echo مشغل الوسائط مع الدبلجة الآلية - نسخة بسيطة
echo ============================================================
echo.

REM التحقق من وجود Python
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ❌ خطأ: لم يتم العثور على Python
    echo.
    pause
    exit /b 1
)

echo ✅ Python متاح
echo.

echo 🚀 تشغيل البرنامج...
echo.

REM تشغيل البرنامج مباشرة
python main.py

REM إذا فشل التشغيل
if %ERRORLEVEL% neq 0 (
    echo.
    echo ❌ خطأ في تشغيل البرنامج!
    echo.
    pause
) else (
    echo.
    echo ✅ تم إغلاق البرنامج بنجاح
)
