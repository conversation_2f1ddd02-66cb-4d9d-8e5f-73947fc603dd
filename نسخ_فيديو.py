#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
أداة نسخ الفيديوهات لمكان آمن
"""

import os
import shutil
import sys

def copy_video_to_safe_location():
    """نسخ الفيديو لمكان آمن"""
    
    # المسار المعقد
    complex_path = r"D:\صور و كورسات\كورسات\kali linux\Red Hat Certification RHCSA - Sander <PERSON> Vugt [Pearson IT Certification]\Chapter01\part00.mp4"
    
    print("🎬 أداة نسخ الفيديو لمكان آمن")
    print("=" * 50)
    print(f"📁 المسار الأصلي: {complex_path}")
    
    # التحقق من وجود الملف
    if not os.path.exists(complex_path):
        print("❌ الملف غير موجود في المسار المحدد")
        print("💡 تأكد من المسار الصحيح")
        return False
    
    # حجم الملف
    file_size = os.path.getsize(complex_path) / (1024 * 1024)
    print(f"📊 حجم الملف: {file_size:.1f} ميجابايت")
    
    # إنشاء مجلد آمن
    safe_dir = "C:\\Videos"
    if not os.path.exists(safe_dir):
        try:
            os.makedirs(safe_dir)
            print(f"✅ تم إنشاء المجلد: {safe_dir}")
        except:
            safe_dir = "Videos"  # في نفس المجلد
            if not os.path.exists(safe_dir):
                os.makedirs(safe_dir)
            print(f"✅ تم إنشاء المجلد: {safe_dir}")
    
    # المسار الآمن
    safe_path = os.path.join(safe_dir, "test_video.mp4")
    
    print(f"📁 المسار الآمن: {safe_path}")
    print("🔄 جاري النسخ...")
    
    try:
        # نسخ الملف
        shutil.copy2(complex_path, safe_path)
        
        if os.path.exists(safe_path):
            new_size = os.path.getsize(safe_path) / (1024 * 1024)
            print(f"✅ تم النسخ بنجاح!")
            print(f"📊 حجم الملف الجديد: {new_size:.1f} ميجابايت")
            print(f"📁 الملف الجديد: {safe_path}")
            
            print("\n" + "=" * 50)
            print("🎉 تم النسخ بنجاح!")
            print("💡 الآن جرب تشغيل الملف الجديد في البرنامج:")
            print(f"   {safe_path}")
            print("=" * 50)
            
            return True
        else:
            print("❌ فشل في النسخ")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في النسخ: {str(e)}")
        return False

if __name__ == "__main__":
    success = copy_video_to_safe_location()
    
    if success:
        print("\n✅ العملية تمت بنجاح!")
        print("الآن افتح البرنامج وجرب الملف الجديد")
    else:
        print("\n❌ فشلت العملية")
    
    input("\nاضغط Enter للخروج...")
