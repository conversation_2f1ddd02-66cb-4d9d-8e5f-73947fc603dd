#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة التسجيل
"""

import os
import logging
from logging.handlers import RotatingFileHandler
from pathlib import Path

def setup_logger(name="media_translator", level=logging.INFO):
    """إعداد المسجل"""
    # إنشاء مجلد السجلات
    log_dir = os.path.join(str(Path.home()), ".media_translator", "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # مسار ملف السجل
    log_file = os.path.join(log_dir, "app.log")
    
    # إنشاء المسجل
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # إنشاء معالج ملف دوار (يحتفظ بآخر 5 ملفات، كل ملف بحجم 5 ميجابايت)
    file_handler = RotatingFileHandler(
        log_file, maxBytes=5*1024*1024, backupCount=5, encoding='utf-8'
    )
    
    # إنشاء معالج وحدة التحكم
    console_handler = logging.StreamHandler()
    
    # تنسيق السجل
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # تعيين المنسق للمعالجات
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # إضافة المعالجات إلى المسجل
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger
