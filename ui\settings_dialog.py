#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة الإعدادات
"""

import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, 
                            Q<PERSON>idget, QLabel, QComboBox, QSpinBox, QDoubleSpinBox, 
                            QCheckBox, QGroupBox, QPushButton, QFileDialog, QLineEdit)
from PyQt5.QtCore import Qt

class SettingsDialog(QDialog):
    """نافذة الإعدادات"""
    
    def __init__(self, config, parent=None):
        """تهيئة نافذة الإعدادات"""
        super().__init__(parent)
        
        self.config = config
        
        self._init_ui()
        self._load_settings()
    
    def _init_ui(self):
        """تهيئة واجهة المستخدم"""
        # تعيين عنوان النافذة
        self.setWindowTitle("الإعدادات")
        self.setMinimumSize(500, 400)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        
        # إنشاء علامات التبويب
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # إنشاء علامة تبويب الإعدادات العامة
        self.general_tab = QWidget()
        self.tab_widget.addTab(self.general_tab, "عام")
        
        # إنشاء علامة تبويب إعدادات الترجمة
        self.translation_tab = QWidget()
        self.tab_widget.addTab(self.translation_tab, "الترجمة")
        
        # إنشاء علامة تبويب إعدادات المسارات
        self.paths_tab = QWidget()
        self.tab_widget.addTab(self.paths_tab, "المسارات")
        
        # إنشاء محتوى علامة تبويب الإعدادات العامة
        self._init_general_tab()
        
        # إنشاء محتوى علامة تبويب إعدادات الترجمة
        self._init_translation_tab()
        
        # إنشاء محتوى علامة تبويب إعدادات المسارات
        self._init_paths_tab()
        
        # إنشاء أزرار التحكم
        button_layout = QHBoxLayout()
        
        # زر حفظ الإعدادات
        self.save_button = QPushButton("حفظ")
        self.save_button.clicked.connect(self.accept)
        button_layout.addWidget(self.save_button)
        
        # زر إلغاء
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        # إضافة أزرار التحكم إلى التخطيط الرئيسي
        layout.addLayout(button_layout)
    
    def _init_general_tab(self):
        """تهيئة علامة تبويب الإعدادات العامة"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self.general_tab)
        
        # إنشاء مجموعة إعدادات المظهر
        appearance_group = QGroupBox("المظهر")
        appearance_layout = QVBoxLayout(appearance_group)
        
        # اختيار السمة
        theme_layout = QHBoxLayout()
        theme_label = QLabel("السمة:")
        self.theme_combo = QComboBox()
        self.theme_combo.addItem("داكنة", "dark")
        self.theme_combo.addItem("فاتحة", "light")
        theme_layout.addWidget(theme_label)
        theme_layout.addWidget(self.theme_combo)
        
        # إضافة اختيار السمة إلى مجموعة المظهر
        appearance_layout.addLayout(theme_layout)
        
        # إضافة مجموعة المظهر إلى التخطيط الرئيسي
        layout.addWidget(appearance_group)
        
        # إنشاء مجموعة إعدادات المشغل
        player_group = QGroupBox("المشغل")
        player_layout = QVBoxLayout(player_group)
        
        # اختيار مستوى الصوت الافتراضي
        volume_layout = QHBoxLayout()
        volume_label = QLabel("مستوى الصوت الافتراضي:")
        self.volume_spin = QSpinBox()
        self.volume_spin.setRange(0, 100)
        self.volume_spin.setSuffix("%")
        volume_layout.addWidget(volume_label)
        volume_layout.addWidget(self.volume_spin)
        
        # إضافة اختيار مستوى الصوت إلى مجموعة المشغل
        player_layout.addLayout(volume_layout)
        
        # إضافة مجموعة المشغل إلى التخطيط الرئيسي
        layout.addWidget(player_group)
        
        # إضافة مساحة مرنة
        layout.addStretch(1)
    
    def _init_translation_tab(self):
        """تهيئة علامة تبويب إعدادات الترجمة"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self.translation_tab)
        
        # إنشاء مجموعة إعدادات اللغة
        language_group = QGroupBox("اللغة")
        language_layout = QVBoxLayout(language_group)
        
        # اختيار لغة المصدر الافتراضية
        source_layout = QHBoxLayout()
        source_label = QLabel("لغة المصدر الافتراضية:")
        self.source_language_combo = QComboBox()
        self.source_language_combo.addItem("تلقائي", "auto")
        self.source_language_combo.addItem("الإنجليزية", "en")
        self.source_language_combo.addItem("العربية", "ar")
        self.source_language_combo.addItem("الفرنسية", "fr")
        self.source_language_combo.addItem("الإسبانية", "es")
        self.source_language_combo.addItem("الألمانية", "de")
        self.source_language_combo.addItem("الإيطالية", "it")
        self.source_language_combo.addItem("الروسية", "ru")
        self.source_language_combo.addItem("الصينية", "zh")
        self.source_language_combo.addItem("اليابانية", "ja")
        source_layout.addWidget(source_label)
        source_layout.addWidget(self.source_language_combo)
        
        # اختيار لغة الهدف الافتراضية
        target_layout = QHBoxLayout()
        target_label = QLabel("لغة الهدف الافتراضية:")
        self.target_language_combo = QComboBox()
        self.target_language_combo.addItem("العربية", "ar")
        self.target_language_combo.addItem("الإنجليزية", "en")
        self.target_language_combo.addItem("الفرنسية", "fr")
        self.target_language_combo.addItem("الإسبانية", "es")
        self.target_language_combo.addItem("الألمانية", "de")
        self.target_language_combo.addItem("الإيطالية", "it")
        self.target_language_combo.addItem("الروسية", "ru")
        self.target_language_combo.addItem("الصينية", "zh")
        self.target_language_combo.addItem("اليابانية", "ja")
        target_layout.addWidget(target_label)
        target_layout.addWidget(self.target_language_combo)
        
        # إضافة اختيارات اللغة إلى مجموعة اللغة
        language_layout.addLayout(source_layout)
        language_layout.addLayout(target_layout)
        
        # إضافة مجموعة اللغة إلى التخطيط الرئيسي
        layout.addWidget(language_group)
        
        # إنشاء مجموعة إعدادات النماذج
        models_group = QGroupBox("النماذج")
        models_layout = QVBoxLayout(models_group)
        
        # اختيار نموذج Whisper
        whisper_layout = QHBoxLayout()
        whisper_label = QLabel("نموذج Whisper:")
        self.whisper_model_combo = QComboBox()
        self.whisper_model_combo.addItem("صغير", "small")
        self.whisper_model_combo.addItem("متوسط", "medium")
        self.whisper_model_combo.addItem("كبير", "large")
        whisper_layout.addWidget(whisper_label)
        whisper_layout.addWidget(self.whisper_model_combo)
        
        # اختيار نموذج TTS
        tts_layout = QHBoxLayout()
        tts_label = QLabel("نموذج TTS:")
        self.tts_model_combo = QComboBox()
        self.tts_model_combo.addItem("عربي - fairseq", "tts_models/ar/fairseq/tts-transformer")
        self.tts_model_combo.addItem("إنجليزي - VITS", "tts_models/en/vctk/vits")
        self.tts_model_combo.addItem("متعدد اللغات - YourTTS", "tts_models/multilingual/your_tts/your_tts")
        tts_layout.addWidget(tts_label)
        tts_layout.addWidget(self.tts_model_combo)
        
        # إضافة اختيارات النماذج إلى مجموعة النماذج
        models_layout.addLayout(whisper_layout)
        models_layout.addLayout(tts_layout)
        
        # إضافة مجموعة النماذج إلى التخطيط الرئيسي
        layout.addWidget(models_group)
        
        # إضافة مساحة مرنة
        layout.addStretch(1)
    
    def _init_paths_tab(self):
        """تهيئة علامة تبويب إعدادات المسارات"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self.paths_tab)
        
        # إنشاء مجموعة المسارات
        paths_group = QGroupBox("المسارات")
        paths_layout = QVBoxLayout(paths_group)
        
        # مسار مجلد النماذج
        models_layout = QHBoxLayout()
        models_label = QLabel("مجلد النماذج:")
        self.models_dir_edit = QLineEdit()
        self.models_dir_edit.setReadOnly(True)
        models_browse_button = QPushButton("تصفح...")
        models_browse_button.clicked.connect(self._browse_models_dir)
        models_layout.addWidget(models_label)
        models_layout.addWidget(self.models_dir_edit)
        models_layout.addWidget(models_browse_button)
        
        # مسار المجلد المؤقت
        temp_layout = QHBoxLayout()
        temp_label = QLabel("المجلد المؤقت:")
        self.temp_dir_edit = QLineEdit()
        self.temp_dir_edit.setReadOnly(True)
        temp_browse_button = QPushButton("تصفح...")
        temp_browse_button.clicked.connect(self._browse_temp_dir)
        temp_layout.addWidget(temp_label)
        temp_layout.addWidget(self.temp_dir_edit)
        temp_layout.addWidget(temp_browse_button)
        
        # مسار مجلد الإخراج
        output_layout = QHBoxLayout()
        output_label = QLabel("مجلد الإخراج:")
        self.output_dir_edit = QLineEdit()
        self.output_dir_edit.setReadOnly(True)
        output_browse_button = QPushButton("تصفح...")
        output_browse_button.clicked.connect(self._browse_output_dir)
        output_layout.addWidget(output_label)
        output_layout.addWidget(self.output_dir_edit)
        output_layout.addWidget(output_browse_button)
        
        # إضافة مسارات المجلدات إلى مجموعة المسارات
        paths_layout.addLayout(models_layout)
        paths_layout.addLayout(temp_layout)
        paths_layout.addLayout(output_layout)
        
        # إضافة مجموعة المسارات إلى التخطيط الرئيسي
        layout.addWidget(paths_group)
        
        # إضافة مساحة مرنة
        layout.addStretch(1)
    
    def _load_settings(self):
        """تحميل الإعدادات"""
        # تحميل إعدادات المظهر
        theme = self.config.get("general", "theme", "dark")
        index = self.theme_combo.findData(theme)
        if index >= 0:
            self.theme_combo.setCurrentIndex(index)
        
        # تحميل إعدادات المشغل
        volume = self.config.get("player", "volume", 70)
        self.volume_spin.setValue(volume)
        
        # تحميل إعدادات اللغة
        source_lang = self.config.get("translation", "source_language", "auto")
        index = self.source_language_combo.findData(source_lang)
        if index >= 0:
            self.source_language_combo.setCurrentIndex(index)
        
        target_lang = self.config.get("translation", "target_language", "ar")
        index = self.target_language_combo.findData(target_lang)
        if index >= 0:
            self.target_language_combo.setCurrentIndex(index)
        
        # تحميل إعدادات النماذج
        whisper_model = self.config.get("translation", "whisper_model", "medium")
        index = self.whisper_model_combo.findData(whisper_model)
        if index >= 0:
            self.whisper_model_combo.setCurrentIndex(index)
        
        tts_model = self.config.get("translation", "tts_model", "tts_models/ar/fairseq/tts-transformer")
        index = self.tts_model_combo.findData(tts_model)
        if index >= 0:
            self.tts_model_combo.setCurrentIndex(index)
        
        # تحميل إعدادات المسارات
        models_dir = self.config.get("paths", "models_dir", "")
        self.models_dir_edit.setText(models_dir)
        
        temp_dir = self.config.get("paths", "temp_dir", "")
        self.temp_dir_edit.setText(temp_dir)
        
        output_dir = self.config.get("paths", "output_dir", "")
        self.output_dir_edit.setText(output_dir)
    
    def _browse_models_dir(self):
        """تصفح مجلد النماذج"""
        dir_path = QFileDialog.getExistingDirectory(
            self, "اختر مجلد النماذج", self.models_dir_edit.text()
        )
        
        if dir_path:
            self.models_dir_edit.setText(dir_path)
    
    def _browse_temp_dir(self):
        """تصفح المجلد المؤقت"""
        dir_path = QFileDialog.getExistingDirectory(
            self, "اختر المجلد المؤقت", self.temp_dir_edit.text()
        )
        
        if dir_path:
            self.temp_dir_edit.setText(dir_path)
    
    def _browse_output_dir(self):
        """تصفح مجلد الإخراج"""
        dir_path = QFileDialog.getExistingDirectory(
            self, "اختر مجلد الإخراج", self.output_dir_edit.text()
        )
        
        if dir_path:
            self.output_dir_edit.setText(dir_path)
    
    def accept(self):
        """حفظ الإعدادات وإغلاق النافذة"""
        # حفظ إعدادات المظهر
        theme = self.theme_combo.currentData()
        self.config.set("general", "theme", theme)
        
        # حفظ إعدادات المشغل
        volume = self.volume_spin.value()
        self.config.set("player", "volume", volume)
        
        # حفظ إعدادات اللغة
        source_lang = self.source_language_combo.currentData()
        self.config.set("translation", "source_language", source_lang)
        
        target_lang = self.target_language_combo.currentData()
        self.config.set("translation", "target_language", target_lang)
        
        # حفظ إعدادات النماذج
        whisper_model = self.whisper_model_combo.currentData()
        self.config.set("translation", "whisper_model", whisper_model)
        
        tts_model = self.tts_model_combo.currentData()
        self.config.set("translation", "tts_model", tts_model)
        
        # حفظ إعدادات المسارات
        models_dir = self.models_dir_edit.text()
        self.config.set("paths", "models_dir", models_dir)
        
        temp_dir = self.temp_dir_edit.text()
        self.config.set("paths", "temp_dir", temp_dir)
        
        output_dir = self.output_dir_edit.text()
        self.config.set("paths", "output_dir", output_dir)
        
        # إغلاق النافذة
        super().accept()
