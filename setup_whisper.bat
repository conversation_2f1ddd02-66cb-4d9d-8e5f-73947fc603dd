@echo off
echo ============================================================
echo أداة إعداد نماذج الذكاء الاصطناعي
echo ============================================================
echo.

REM إنشاء مجلدات النماذج
echo جاري إنشاء مجلدات النماذج...
if not exist "models" mkdir "models"
if not exist "models\whisper" mkdir "models\whisper"
if not exist "models\translation" mkdir "models\translation"
if not exist "models\tts" mkdir "models\tts"

REM إنشاء مجلد cache إذا لم يكن موجوداً
if not exist "%USERPROFILE%\.cache\whisper" (
    mkdir "%USERPROFILE%\.cache\whisper"
)

echo جاري البحث عن نموذج Whisper في جهازك...
echo.

REM تشغيل سكريبت Python للبحث عن النموذج
python check_whisper_model.py

echo.
echo جاري التحقق من النماذج الأخرى...
python -c "
try:
    from transformers import MarianMTModel, MarianTokenizer
    print('✓ نماذج الترجمة متوفرة')
except ImportError:
    print('✗ نماذج الترجمة غير متوفرة - سيتم تنزيلها عند الحاجة')

try:
    from TTS.api import TTS
    print('✓ نماذج TTS متوفرة')
except ImportError:
    print('✗ نماذج TTS غير متوفرة - سيتم استخدام gTTS كبديل')
"

echo.
echo تم الانتهاء من إعداد النماذج.
echo.
echo اضغط أي مفتاح للخروج...
pause > nul
