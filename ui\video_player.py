#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مشغل فيديو بسيط وموثوق
"""

import os
import sys
import tempfile
import shutil
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QSlider, QLabel, QStyle)
from PyQt5.QtCore import Qt, pyqtSignal, QUrl
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtMultimediaWidgets import QVideoWidget

class VideoPlayer(QWidget):
    """مشغل فيديو بسيط"""
    
    # الإشارات
    media_loaded = pyqtSignal(bool)
    playback_state_changed = pyqtSignal(bool)
    
    def __init__(self, config=None):
        super().__init__()
        self.config = config
        self.current_file = None
        self.temp_file = None
        self.is_playing = False
        
        self.init_ui()
        self.init_player()
        self.connect_signals()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout()
        
        # مشغل الفيديو
        self.video_widget = QVideoWidget()
        self.video_widget.setMinimumHeight(300)
        layout.addWidget(self.video_widget)
        
        # أزرار التحكم
        controls_layout = QHBoxLayout()
        
        # زر التشغيل/الإيقاف
        self.play_button = QPushButton()
        self.play_button.setIcon(self.style().standardIcon(QStyle.SP_MediaPlay))
        self.play_button.clicked.connect(self.toggle_play)
        self.play_button.setEnabled(False)
        controls_layout.addWidget(self.play_button)
        
        # زر الإيقاف
        self.stop_button = QPushButton()
        self.stop_button.setIcon(self.style().standardIcon(QStyle.SP_MediaStop))
        self.stop_button.clicked.connect(self.stop)
        self.stop_button.setEnabled(False)
        controls_layout.addWidget(self.stop_button)
        
        # شريط التقدم
        self.position_slider = QSlider(Qt.Horizontal)
        self.position_slider.setRange(0, 1000)
        self.position_slider.setValue(0)
        controls_layout.addWidget(self.position_slider)
        
        # تسمية الوقت
        self.time_label = QLabel("00:00 / 00:00")
        controls_layout.addWidget(self.time_label)
        
        # زر كتم الصوت
        self.mute_button = QPushButton()
        self.mute_button.setIcon(self.style().standardIcon(QStyle.SP_MediaVolume))
        self.mute_button.clicked.connect(self.toggle_mute)
        controls_layout.addWidget(self.mute_button)
        
        # شريط مستوى الصوت
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(70)
        self.volume_slider.setMaximumWidth(100)
        controls_layout.addWidget(self.volume_slider)
        
        layout.addLayout(controls_layout)
        self.setLayout(layout)
    
    def init_player(self):
        """تهيئة مشغل الوسائط"""
        self.player = QMediaPlayer()
        self.player.setVideoOutput(self.video_widget)
        self.player.setVolume(70)
    
    def connect_signals(self):
        """ربط الإشارات"""
        self.player.stateChanged.connect(self.on_state_changed)
        self.player.positionChanged.connect(self.on_position_changed)
        self.player.durationChanged.connect(self.on_duration_changed)
        self.player.mediaStatusChanged.connect(self.on_media_status_changed)
        self.player.error.connect(self.on_error)
        
        self.position_slider.sliderMoved.connect(self.set_position)
        self.volume_slider.valueChanged.connect(self.set_volume)
    
    def create_safe_copy(self, file_path):
        """إنشاء نسخة آمنة من الملف"""
        try:
            print(f"🔄 إنشاء نسخة آمنة للملف...")
            
            # تنظيف النسخة السابقة
            if self.temp_file and os.path.exists(self.temp_file):
                try:
                    os.remove(self.temp_file)
                except:
                    pass
            
            # إنشاء مجلد مؤقت
            temp_dir = tempfile.mkdtemp(prefix="video_")
            file_ext = os.path.splitext(file_path)[1]
            safe_filename = f"video{file_ext}"
            safe_path = os.path.join(temp_dir, safe_filename)
            
            print(f"📋 نسخ الملف...")
            shutil.copy2(file_path, safe_path)
            
            if os.path.exists(safe_path):
                self.temp_file = safe_path
                print(f"✅ تم إنشاء النسخة الآمنة")
                return safe_path
            else:
                print(f"❌ فشل في إنشاء النسخة الآمنة")
                return None
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء النسخة الآمنة: {str(e)}")
            return None
    
    def load_media(self, file_path):
        """تحميل ملف وسائط"""
        print("=" * 50)
        print(f"🎬 تحميل الملف: {os.path.basename(file_path)}")
        print(f"📁 المسار: {file_path}")
        
        if not os.path.exists(file_path):
            print("❌ الملف غير موجود!")
            self.media_loaded.emit(False)
            return
        
        # فحص المسار للمشاكل
        needs_copy = False
        if any(ord(char) > 127 for char in file_path):
            print("⚠️ المسار يحتوي على أحرف عربية")
            needs_copy = True
        
        if '[' in file_path or ']' in file_path:
            print("⚠️ المسار يحتوي على أقواس مربعة")
            needs_copy = True
        
        if len(file_path) > 200:
            print("⚠️ المسار طويل جداً")
            needs_copy = True
        
        # تحديد المسار المستخدم
        if needs_copy:
            print("🔄 سيتم إنشاء نسخة آمنة...")
            working_path = self.create_safe_copy(file_path)
            if not working_path:
                print("❌ فشل في إنشاء النسخة الآمنة، سيتم استخدام المسار الأصلي")
                working_path = file_path
        else:
            print("✅ المسار آمن")
            working_path = file_path
        
        self.current_file = file_path
        
        try:
            print("📺 تحميل في المشغل...")
            file_url = QUrl.fromLocalFile(os.path.abspath(working_path))
            media_content = QMediaContent(file_url)
            self.player.setMedia(media_content)
            
            # إعادة تعيين واجهة المستخدم
            self.position_slider.setValue(0)
            self.time_label.setText("00:00 / 00:00")
            
            print("✅ تم إرسال الملف للمشغل")
            print("=" * 50)
            
        except Exception as e:
            print(f"❌ خطأ في التحميل: {str(e)}")
            print("=" * 50)
            self.media_loaded.emit(False)
    
    def toggle_play(self):
        """تبديل حالة التشغيل"""
        if self.player.state() == QMediaPlayer.PlayingState:
            self.player.pause()
        else:
            self.player.play()
    
    def stop(self):
        """إيقاف التشغيل"""
        self.player.stop()
        self.position_slider.setValue(0)
        self.time_label.setText("00:00 / 00:00")
    
    def set_position(self, position):
        """تعيين موضع التشغيل"""
        if self.player.duration() > 0:
            new_position = int(position * self.player.duration() / 1000)
            self.player.setPosition(new_position)
    
    def set_volume(self, volume):
        """تعيين مستوى الصوت"""
        self.player.setVolume(volume)
        if self.config:
            self.config.set("player", "volume", volume)
    
    def toggle_mute(self):
        """تبديل كتم الصوت"""
        if self.player.isMuted():
            self.player.setMuted(False)
            self.mute_button.setIcon(self.style().standardIcon(QStyle.SP_MediaVolume))
        else:
            self.player.setMuted(True)
            self.mute_button.setIcon(self.style().standardIcon(QStyle.SP_MediaVolumeMuted))
    
    def toggle_fullscreen(self):
        """تبديل وضع ملء الشاشة"""
        if self.video_widget.isFullScreen():
            self.video_widget.showNormal()
        else:
            self.video_widget.showFullScreen()
    
    def on_state_changed(self, state):
        """معالجة تغيير حالة المشغل"""
        if state == QMediaPlayer.PlayingState:
            self.play_button.setIcon(self.style().standardIcon(QStyle.SP_MediaPause))
            self.is_playing = True
            self.playback_state_changed.emit(True)
            print("▶️ التشغيل نشط")
        else:
            self.play_button.setIcon(self.style().standardIcon(QStyle.SP_MediaPlay))
            self.is_playing = False
            self.playback_state_changed.emit(False)
            print("⏸️ التشغيل متوقف")
    
    def on_position_changed(self, position):
        """معالجة تغيير موضع التشغيل"""
        if self.player.duration() > 0:
            slider_value = int(position * 1000 / self.player.duration())
            self.position_slider.blockSignals(True)
            self.position_slider.setValue(slider_value)
            self.position_slider.blockSignals(False)
            
            # تحديث عرض الوقت
            position_str = self.format_time(position)
            duration_str = self.format_time(self.player.duration())
            self.time_label.setText(f"{position_str} / {duration_str}")
    
    def on_duration_changed(self, duration):
        """معالجة تغيير مدة الوسائط"""
        duration_str = self.format_time(duration)
        self.time_label.setText(f"00:00 / {duration_str}")
        print(f"⏱️ مدة الفيديو: {duration_str}")
    
    def on_media_status_changed(self, status):
        """معالجة تغيير حالة الوسائط"""
        status_names = {
            QMediaPlayer.NoMedia: "لا توجد وسائط",
            QMediaPlayer.LoadingMedia: "جاري التحميل",
            QMediaPlayer.LoadedMedia: "تم التحميل بنجاح",
            QMediaPlayer.StalledMedia: "متوقف",
            QMediaPlayer.BufferingMedia: "جاري التخزين المؤقت",
            QMediaPlayer.BufferedMedia: "جاهز للتشغيل",
            QMediaPlayer.EndOfMedia: "انتهى الملف",
            QMediaPlayer.InvalidMedia: "ملف غير صالح"
        }
        
        status_name = status_names.get(status, f"غير معروف ({status})")
        print(f"📊 حالة الوسائط: {status_name}")
        
        if status == QMediaPlayer.LoadedMedia:
            print("🎉 نجح تحميل الفيديو!")
            self.play_button.setEnabled(True)
            self.stop_button.setEnabled(True)
            self.media_loaded.emit(True)
        elif status == QMediaPlayer.InvalidMedia:
            print("❌ فشل تحميل الفيديو")
            self.media_loaded.emit(False)
    
    def on_error(self, error):
        """معالجة أخطاء المشغل"""
        error_names = {
            QMediaPlayer.NoError: "لا توجد أخطاء",
            QMediaPlayer.ResourceError: "خطأ في المورد",
            QMediaPlayer.FormatError: "خطأ في التنسيق",
            QMediaPlayer.NetworkError: "خطأ في الشبكة",
            QMediaPlayer.AccessDeniedError: "رفض الوصول"
        }
        
        error_name = error_names.get(error, f"خطأ غير معروف ({error})")
        print(f"🚨 خطأ في المشغل: {error_name}")
        
        if error != QMediaPlayer.NoError:
            self.media_loaded.emit(False)
    
    def format_time(self, milliseconds):
        """تنسيق الوقت"""
        seconds = int(milliseconds / 1000)
        minutes = seconds // 60
        seconds %= 60
        return f"{minutes:02d}:{seconds:02d}"
    
    def get_duration(self):
        """الحصول على مدة الوسائط"""
        return self.player.duration()
    
    def get_position(self):
        """الحصول على موضع التشغيل الحالي"""
        return self.player.position()
    
    def cleanup(self):
        """تنظيف الموارد"""
        if self.temp_file and os.path.exists(self.temp_file):
            try:
                temp_dir = os.path.dirname(self.temp_file)
                shutil.rmtree(temp_dir)
                print(f"🗑️ تم حذف الملف المؤقت")
            except:
                pass
