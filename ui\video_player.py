#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مكون مشغل الفيديو
"""

import os
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QSlider, QLabel, QStyle, QSizePolicy, QFrame)
from PyQt5.QtCore import Qt, QUrl, QTimer, pyqtSignal, QSize
from PyQt5.QtGui import QIcon, QKeySequence
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtMultimediaWidgets import QVideoWidget

class VideoPlayer(QWidget):
    """مكون مشغل الفيديو"""
    
    # الإشارات
    media_loaded = pyqtSignal(bool)  # نجاح تحميل الوسائط
    playback_state_changed = pyqtSignal(bool)  # تغيير حالة التشغيل (تشغيل/إيقاف)
    
    def __init__(self, config):
        """تهيئة مشغل الفيديو"""
        super().__init__()
        
        self.config = config
        self.is_fullscreen = False
        self.is_muted = False
        
        self._init_ui()
        self._connect_signals()
        
        # تطبيق الإعدادات
        self.apply_settings()
    
    def _init_ui(self):
        """تهيئة واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # إنشاء مكون عرض الفيديو
        self.video_widget = QVideoWidget()
        self.video_widget.setMinimumSize(320, 180)
        layout.addWidget(self.video_widget)
        
        # إنشاء مشغل الوسائط
        self.media_player = QMediaPlayer(None, QMediaPlayer.VideoSurface)
        self.media_player.setVideoOutput(self.video_widget)
        
        # إنشاء شريط التحكم
        control_layout = QHBoxLayout()
        control_layout.setContentsMargins(5, 5, 5, 5)
        
        # زر التشغيل/الإيقاف المؤقت
        self.play_button = QPushButton()
        self.play_button.setIcon(self.style().standardIcon(QStyle.SP_MediaPlay))
        self.play_button.setIconSize(QSize(24, 24))
        self.play_button.setToolTip("تشغيل/إيقاف مؤقت")
        control_layout.addWidget(self.play_button)
        
        # زر الإيقاف
        self.stop_button = QPushButton()
        self.stop_button.setIcon(self.style().standardIcon(QStyle.SP_MediaStop))
        self.stop_button.setIconSize(QSize(24, 24))
        self.stop_button.setToolTip("إيقاف")
        control_layout.addWidget(self.stop_button)
        
        # شريط التقدم
        self.position_slider = QSlider(Qt.Horizontal)
        self.position_slider.setRange(0, 0)
        self.position_slider.setToolTip("موضع التشغيل")
        control_layout.addWidget(self.position_slider)
        
        # عرض الوقت
        self.time_label = QLabel("00:00 / 00:00")
        self.time_label.setMinimumWidth(100)
        self.time_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        control_layout.addWidget(self.time_label)
        
        # زر كتم الصوت
        self.mute_button = QPushButton()
        self.mute_button.setIcon(self.style().standardIcon(QStyle.SP_MediaVolume))
        self.mute_button.setIconSize(QSize(24, 24))
        self.mute_button.setToolTip("كتم الصوت")
        control_layout.addWidget(self.mute_button)
        
        # شريط مستوى الصوت
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(70)
        self.volume_slider.setMaximumWidth(100)
        self.volume_slider.setToolTip("مستوى الصوت")
        control_layout.addWidget(self.volume_slider)
        
        # زر ملء الشاشة
        self.fullscreen_button = QPushButton()
        self.fullscreen_button.setIcon(self.style().standardIcon(QStyle.SP_TitleBarMaxButton))
        self.fullscreen_button.setIconSize(QSize(24, 24))
        self.fullscreen_button.setToolTip("ملء الشاشة")
        control_layout.addWidget(self.fullscreen_button)
        
        # إضافة شريط التحكم إلى التخطيط الرئيسي
        layout.addLayout(control_layout)
        
        # إنشاء مؤقت لتحديث شريط التقدم
        self.update_timer = QTimer(self)
        self.update_timer.setInterval(1000)  # تحديث كل ثانية
    
    def _connect_signals(self):
        """ربط الإشارات"""
        # ربط إشارات الأزرار
        self.play_button.clicked.connect(self.toggle_play)
        self.stop_button.clicked.connect(self.stop)
        self.mute_button.clicked.connect(self.toggle_mute)
        self.fullscreen_button.clicked.connect(self.toggle_fullscreen)
        
        # ربط إشارات أشرطة التمرير
        self.position_slider.sliderMoved.connect(self.set_position)
        self.volume_slider.valueChanged.connect(self.set_volume)
        
        # ربط إشارات مشغل الوسائط
        self.media_player.stateChanged.connect(self._on_state_changed)
        self.media_player.positionChanged.connect(self._on_position_changed)
        self.media_player.durationChanged.connect(self._on_duration_changed)
        self.media_player.mediaStatusChanged.connect(self._on_media_status_changed)
        
        # ربط إشارة المؤقت
        self.update_timer.timeout.connect(self._update_ui)
    
    def apply_settings(self):
        """تطبيق الإعدادات"""
        # تعيين مستوى الصوت
        volume = self.config.get("player", "volume", 70)
        self.volume_slider.setValue(volume)
        self.media_player.setVolume(volume)
    
    def load_media(self, file_path):
        """تحميل ملف وسائط"""
        if not os.path.exists(file_path):
            self.media_loaded.emit(False)
            return
        
        # تحميل الملف
        self.media_player.setMedia(QMediaContent(QUrl.fromLocalFile(file_path)))
        
        # إعادة تعيين واجهة المستخدم
        self.position_slider.setValue(0)
        self.time_label.setText("00:00 / 00:00")
        
        # بدء التشغيل تلقائيًا
        self.media_player.play()
        self.update_timer.start()
    
    def toggle_play(self):
        """تبديل حالة التشغيل (تشغيل/إيقاف مؤقت)"""
        if self.media_player.state() == QMediaPlayer.PlayingState:
            self.media_player.pause()
        else:
            self.media_player.play()
    
    def stop(self):
        """إيقاف التشغيل"""
        self.media_player.stop()
        self.update_timer.stop()
    
    def toggle_mute(self):
        """تبديل حالة كتم الصوت"""
        self.is_muted = not self.is_muted
        self.media_player.setMuted(self.is_muted)
        
        # تحديث أيقونة الزر
        if self.is_muted:
            self.mute_button.setIcon(self.style().standardIcon(QStyle.SP_MediaVolumeMuted))
        else:
            self.mute_button.setIcon(self.style().standardIcon(QStyle.SP_MediaVolume))
    
    def toggle_fullscreen(self):
        """تبديل وضع ملء الشاشة"""
        if self.is_fullscreen:
            # العودة من وضع ملء الشاشة
            self.video_widget.setParent(self)
            self.layout().insertWidget(0, self.video_widget)
            self.video_widget.showNormal()
            self.fullscreen_button.setIcon(self.style().standardIcon(QStyle.SP_TitleBarMaxButton))
        else:
            # الانتقال إلى وضع ملء الشاشة
            self.video_widget.setParent(None)
            self.video_widget.showFullScreen()
            self.fullscreen_button.setIcon(self.style().standardIcon(QStyle.SP_TitleBarNormalButton))
        
        self.is_fullscreen = not self.is_fullscreen
    
    def set_position(self, position):
        """تعيين موضع التشغيل"""
        self.media_player.setPosition(position)
    
    def set_volume(self, volume):
        """تعيين مستوى الصوت"""
        self.media_player.setVolume(volume)
        self.config.set("player", "volume", volume)
        
        # إلغاء كتم الصوت إذا كان مكتومًا وتم رفع مستوى الصوت
        if self.is_muted and volume > 0:
            self.toggle_mute()
    
    def _on_state_changed(self, state):
        """معالجة حدث تغيير حالة التشغيل"""
        if state == QMediaPlayer.PlayingState:
            self.play_button.setIcon(self.style().standardIcon(QStyle.SP_MediaPause))
            self.update_timer.start()
            self.playback_state_changed.emit(True)
        else:
            self.play_button.setIcon(self.style().standardIcon(QStyle.SP_MediaPlay))
            if state == QMediaPlayer.StoppedState:
                self.update_timer.stop()
            self.playback_state_changed.emit(False)
    
    def _on_position_changed(self, position):
        """معالجة حدث تغيير موضع التشغيل"""
        # تحديث شريط التقدم بدون تفعيل إشارة sliderMoved
        self.position_slider.blockSignals(True)
        self.position_slider.setValue(position)
        self.position_slider.blockSignals(False)
        
        # تحديث عرض الوقت
        duration = self.media_player.duration()
        self._update_time_label(position, duration)
    
    def _on_duration_changed(self, duration):
        """معالجة حدث تغيير مدة الوسائط"""
        self.position_slider.setRange(0, duration)
        
        # تحديث عرض الوقت
        position = self.media_player.position()
        self._update_time_label(position, duration)
    
    def _on_media_status_changed(self, status):
        """معالجة حدث تغيير حالة الوسائط"""
        if status == QMediaPlayer.LoadedMedia:
            self.media_loaded.emit(True)
        elif status == QMediaPlayer.InvalidMedia:
            self.media_loaded.emit(False)
    
    def _update_ui(self):
        """تحديث واجهة المستخدم"""
        # تحديث موضع التشغيل
        position = self.media_player.position()
        duration = self.media_player.duration()
        
        # تحديث شريط التقدم
        self.position_slider.blockSignals(True)
        self.position_slider.setValue(position)
        self.position_slider.blockSignals(False)
        
        # تحديث عرض الوقت
        self._update_time_label(position, duration)
    
    def _update_time_label(self, position, duration):
        """تحديث عرض الوقت"""
        position_str = self._format_time(position)
        duration_str = self._format_time(duration)
        self.time_label.setText(f"{position_str} / {duration_str}")
    
    def _format_time(self, milliseconds):
        """تنسيق الوقت بالثواني والدقائق"""
        seconds = int(milliseconds / 1000)
        minutes = seconds // 60
        seconds %= 60
        return f"{minutes:02d}:{seconds:02d}"
    
    def keyPressEvent(self, event):
        """معالجة أحداث المفاتيح"""
        key = event.key()
        
        if key == Qt.Key_Space:
            # تشغيل/إيقاف مؤقت
            self.toggle_play()
        elif key == Qt.Key_F:
            # ملء الشاشة
            self.toggle_fullscreen()
        elif key == Qt.Key_M:
            # كتم الصوت
            self.toggle_mute()
        elif key == Qt.Key_Right:
            # تقديم 5 ثوانٍ
            self.media_player.setPosition(self.media_player.position() + 5000)
        elif key == Qt.Key_Left:
            # إرجاع 5 ثوانٍ
            self.media_player.setPosition(max(0, self.media_player.position() - 5000))
        elif key == Qt.Key_Up:
            # رفع مستوى الصوت
            self.volume_slider.setValue(min(100, self.volume_slider.value() + 5))
        elif key == Qt.Key_Down:
            # خفض مستوى الصوت
            self.volume_slider.setValue(max(0, self.volume_slider.value() - 5))
        elif key == Qt.Key_Escape and self.is_fullscreen:
            # الخروج من وضع ملء الشاشة
            self.toggle_fullscreen()
        else:
            # تمرير الحدث إلى الفئة الأساسية
            super().keyPressEvent(event)
