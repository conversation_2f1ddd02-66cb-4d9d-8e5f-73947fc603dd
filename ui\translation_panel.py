#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
لوحة الترجمة والدبلجة
"""

import os
import threading
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QTabWidget, QTextEdit, QComboBox,
                            QSpinBox, QDoubleSpinBox, QCheckBox, QGroupBox,
                            QRadioButton, QProgressBar, QSplitter, QListWidget,
                            QMessageBox, QFileDialog, QStyle)
from PyQt5.QtCore import Qt, QUrl, pyqtSignal, QThread, QTimer
from PyQt5.QtGui import QIcon, QFont
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent

class TranslationPanel(QWidget):
    """لوحة الترجمة والدبلجة"""

    # الإشارات
    text_extracted = pyqtSignal(bool)  # نجاح استخراج النص
    text_translated = pyqtSignal(bool)  # نجاح ترجمة النص
    audio_generated = pyqtSignal(bool)  # نجاح توليد الصوت
    video_dubbed = pyqtSignal(bool, str)  # نجاح دبلجة الفيديو (النجاح، مسار الملف)
    progress_updated = pyqtSignal(int, str)  # تحديث التقدم (النسبة، الرسالة)

    def __init__(self, config, translation_engine, tts_engine):
        """تهيئة لوحة الترجمة"""
        super().__init__()

        self.config = config
        self.translation_engine = translation_engine
        self.tts_engine = tts_engine

        self.extracted_text = ""
        self.translated_text = ""
        self.audio_path = ""
        self.dubbed_video_path = ""

        self.is_processing = False

        self._init_ui()
        self._connect_signals()

        # تطبيق الإعدادات
        self.apply_settings()

    def _init_ui(self):
        """تهيئة واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # إنشاء علامات التبويب
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)

        # إنشاء علامة تبويب الترجمة
        self.translation_tab = QWidget()
        self.tab_widget.addTab(self.translation_tab, "الترجمة")

        # إنشاء علامة تبويب الصوت
        self.audio_tab = QWidget()
        self.tab_widget.addTab(self.audio_tab, "الصوت")

        # إنشاء علامة تبويب الدبلجة
        self.dubbing_tab = QWidget()
        self.tab_widget.addTab(self.dubbing_tab, "الدبلجة")

        # إنشاء محتوى علامة تبويب الترجمة
        self._init_translation_tab()

        # إنشاء محتوى علامة تبويب الصوت
        self._init_audio_tab()

        # إنشاء محتوى علامة تبويب الدبلجة
        self._init_dubbing_tab()

        # إنشاء أزرار التحكم
        control_layout = QHBoxLayout()

        # زر استخراج النص
        self.extract_button = QPushButton("استخراج النص")
        control_layout.addWidget(self.extract_button)

        # زر ترجمة النص
        self.translate_button = QPushButton("ترجمة النص")
        self.translate_button.setEnabled(False)
        control_layout.addWidget(self.translate_button)

        # زر توليد الصوت
        self.generate_audio_button = QPushButton("توليد الصوت")
        self.generate_audio_button.setEnabled(False)
        control_layout.addWidget(self.generate_audio_button)

        # زر دبلجة الفيديو
        self.dub_button = QPushButton("دبلجة الفيديو")
        self.dub_button.setEnabled(False)
        control_layout.addWidget(self.dub_button)

        # زر الدبلجة الشاملة (كل شيء في خطوة واحدة)
        self.full_dub_button = QPushButton("دبلجة شاملة")
        self.full_dub_button.setStyleSheet("""
            QPushButton {
                background-color: #2E8B57;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #3CB371;
            }
            QPushButton:disabled {
                background-color: #696969;
            }
        """)
        self.full_dub_button.setToolTip("استخراج النص → ترجمة → توليد الصوت → دبلجة الفيديو")
        control_layout.addWidget(self.full_dub_button)

        # إضافة أزرار التحكم إلى التخطيط الرئيسي
        layout.addLayout(control_layout)

        # إنشاء شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

    def _init_translation_tab(self):
        """تهيئة علامة تبويب الترجمة"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self.translation_tab)

        # إنشاء مجموعة إعدادات الترجمة
        settings_group = QGroupBox("إعدادات الترجمة")
        settings_layout = QHBoxLayout(settings_group)

        # اختيار لغة المصدر
        source_layout = QVBoxLayout()
        source_label = QLabel("لغة المصدر:")
        self.source_language_combo = QComboBox()
        self.source_language_combo.addItem("تلقائي", "auto")
        self.source_language_combo.addItem("الإنجليزية", "en")
        self.source_language_combo.addItem("العربية", "ar")
        self.source_language_combo.addItem("الفرنسية", "fr")
        self.source_language_combo.addItem("الإسبانية", "es")
        self.source_language_combo.addItem("الألمانية", "de")
        self.source_language_combo.addItem("الإيطالية", "it")
        self.source_language_combo.addItem("الروسية", "ru")
        self.source_language_combo.addItem("الصينية", "zh")
        self.source_language_combo.addItem("اليابانية", "ja")
        source_layout.addWidget(source_label)
        source_layout.addWidget(self.source_language_combo)
        settings_layout.addLayout(source_layout)

        # اختيار لغة الهدف
        target_layout = QVBoxLayout()
        target_label = QLabel("لغة الهدف:")
        self.target_language_combo = QComboBox()
        self.target_language_combo.addItem("العربية", "ar")
        self.target_language_combo.addItem("الإنجليزية", "en")
        self.target_language_combo.addItem("الفرنسية", "fr")
        self.target_language_combo.addItem("الإسبانية", "es")
        self.target_language_combo.addItem("الألمانية", "de")
        self.target_language_combo.addItem("الإيطالية", "it")
        self.target_language_combo.addItem("الروسية", "ru")
        self.target_language_combo.addItem("الصينية", "zh")
        self.target_language_combo.addItem("اليابانية", "ja")
        target_layout.addWidget(target_label)
        target_layout.addWidget(self.target_language_combo)
        settings_layout.addLayout(target_layout)

        # اختيار نموذج Whisper
        whisper_layout = QVBoxLayout()
        whisper_label = QLabel("نموذج Whisper:")
        self.whisper_model_combo = QComboBox()
        self.whisper_model_combo.addItem("صغير", "small")
        self.whisper_model_combo.addItem("متوسط", "medium")
        self.whisper_model_combo.addItem("كبير", "large")
        whisper_layout.addWidget(whisper_label)
        whisper_layout.addWidget(self.whisper_model_combo)
        settings_layout.addLayout(whisper_layout)

        # إضافة مجموعة الإعدادات إلى التخطيط الرئيسي
        layout.addWidget(settings_group)

        # إنشاء مقسم للنص المستخرج والمترجم
        text_splitter = QSplitter(Qt.Vertical)
        layout.addWidget(text_splitter, 1)  # إعطاء وزن أكبر للمقسم

        # إنشاء مجموعة النص المستخرج
        source_text_group = QGroupBox("النص المستخرج")
        source_text_layout = QVBoxLayout(source_text_group)

        # محرر النص المستخرج
        self.source_text_edit = QTextEdit()
        self.source_text_edit.setReadOnly(False)  # يمكن تحريره يدويًا
        self.source_text_edit.setPlaceholderText("سيظهر هنا النص المستخرج من الفيديو...")
        source_text_layout.addWidget(self.source_text_edit)

        # إضافة مجموعة النص المستخرج إلى المقسم
        text_splitter.addWidget(source_text_group)

        # إنشاء مجموعة النص المترجم
        translated_text_group = QGroupBox("النص المترجم")
        translated_text_layout = QVBoxLayout(translated_text_group)

        # محرر النص المترجم
        self.translated_text_edit = QTextEdit()
        self.translated_text_edit.setReadOnly(False)  # يمكن تحريره يدويًا
        self.translated_text_edit.setPlaceholderText("سيظهر هنا النص المترجم...")
        translated_text_layout.addWidget(self.translated_text_edit)

        # إضافة مجموعة النص المترجم إلى المقسم
        text_splitter.addWidget(translated_text_group)

        # تعيين أحجام المقسم
        text_splitter.setSizes([200, 200])

    def _init_audio_tab(self):
        """تهيئة علامة تبويب الصوت"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self.audio_tab)

        # إنشاء مجموعة إعدادات الصوت
        settings_group = QGroupBox("إعدادات الصوت")
        settings_layout = QHBoxLayout(settings_group)

        # اختيار نموذج TTS
        tts_layout = QVBoxLayout()
        tts_label = QLabel("نموذج TTS:")
        self.tts_model_combo = QComboBox()
        self.tts_model_combo.addItem("عربي - fairseq", "tts_models/ar/fairseq/tts-transformer")
        self.tts_model_combo.addItem("إنجليزي - VITS", "tts_models/en/vctk/vits")
        self.tts_model_combo.addItem("متعدد اللغات - YourTTS", "tts_models/multilingual/your_tts/your_tts")
        tts_layout.addWidget(tts_label)
        tts_layout.addWidget(self.tts_model_combo)
        settings_layout.addLayout(tts_layout)

        # اختيار نوع الصوت
        voice_layout = QVBoxLayout()
        voice_label = QLabel("نوع الصوت:")
        self.voice_gender_combo = QComboBox()
        self.voice_gender_combo.addItem("ذكر", "male")
        self.voice_gender_combo.addItem("أنثى", "female")
        voice_layout.addWidget(voice_label)
        voice_layout.addWidget(self.voice_gender_combo)
        settings_layout.addLayout(voice_layout)

        # ضبط سرعة الكلام
        speed_layout = QVBoxLayout()
        speed_label = QLabel("سرعة الكلام:")
        self.speech_rate_spin = QDoubleSpinBox()
        self.speech_rate_spin.setRange(0.5, 2.0)
        self.speech_rate_spin.setSingleStep(0.1)
        self.speech_rate_spin.setValue(1.0)
        speed_layout.addWidget(speed_label)
        speed_layout.addWidget(self.speech_rate_spin)
        settings_layout.addLayout(speed_layout)

        # إضافة مجموعة الإعدادات إلى التخطيط الرئيسي
        layout.addWidget(settings_group)

        # إنشاء مجموعة معاينة الصوت
        preview_group = QGroupBox("معاينة الصوت")
        preview_layout = QVBoxLayout(preview_group)

        # زر توليد عينة صوتية
        self.generate_sample_button = QPushButton("توليد عينة صوتية")
        preview_layout.addWidget(self.generate_sample_button)

        # أزرار التحكم في تشغيل الصوت
        audio_controls_layout = QHBoxLayout()

        # زر تشغيل الصوت
        self.play_audio_button = QPushButton("تشغيل")
        self.play_audio_button.setIcon(self.style().standardIcon(QStyle.SP_MediaPlay))
        self.play_audio_button.setEnabled(False)
        audio_controls_layout.addWidget(self.play_audio_button)

        # زر إيقاف الصوت
        self.stop_audio_button = QPushButton("إيقاف")
        self.stop_audio_button.setIcon(self.style().standardIcon(QStyle.SP_MediaStop))
        self.stop_audio_button.setEnabled(False)
        audio_controls_layout.addWidget(self.stop_audio_button)

        # إضافة أزرار التحكم إلى تخطيط المعاينة
        preview_layout.addLayout(audio_controls_layout)

        # إضافة مجموعة المعاينة إلى التخطيط الرئيسي
        layout.addWidget(preview_group)

        # إنشاء مجموعة خيارات متقدمة
        advanced_group = QGroupBox("خيارات متقدمة")
        advanced_layout = QVBoxLayout(advanced_group)

        # خيار تحسين جودة الصوت
        self.enhance_audio_check = QCheckBox("تحسين جودة الصوت")
        self.enhance_audio_check.setChecked(True)
        advanced_layout.addWidget(self.enhance_audio_check)

        # خيار مزامنة الشفاه
        self.lip_sync_check = QCheckBox("مزامنة الشفاه (تجريبي)")
        self.lip_sync_check.setChecked(False)
        advanced_layout.addWidget(self.lip_sync_check)

        # خيار التعرف على المتحدثين
        self.speaker_diarization_check = QCheckBox("التعرف على المتحدثين المختلفين")
        self.speaker_diarization_check.setChecked(False)
        advanced_layout.addWidget(self.speaker_diarization_check)

        # إضافة مجموعة الخيارات المتقدمة إلى التخطيط الرئيسي
        layout.addWidget(advanced_group)

        # إضافة مساحة مرنة
        layout.addStretch(1)

    def _init_dubbing_tab(self):
        """تهيئة علامة تبويب الدبلجة"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self.dubbing_tab)

        # إنشاء مجموعة إعدادات الدبلجة
        settings_group = QGroupBox("إعدادات الدبلجة")
        settings_layout = QVBoxLayout(settings_group)

        # خيار دمج الصوت الأصلي
        audio_options_layout = QHBoxLayout()

        # خيار كتم الصوت الأصلي
        self.mute_original_check = QCheckBox("كتم الصوت الأصلي")
        self.mute_original_check.setChecked(False)
        audio_options_layout.addWidget(self.mute_original_check)

        # خيار خفض مستوى الصوت الأصلي
        self.lower_original_check = QCheckBox("خفض مستوى الصوت الأصلي")
        self.lower_original_check.setChecked(True)
        audio_options_layout.addWidget(self.lower_original_check)

        # إضافة خيارات الصوت إلى تخطيط الإعدادات
        settings_layout.addLayout(audio_options_layout)

        # خيار جودة الفيديو
        quality_layout = QHBoxLayout()
        quality_label = QLabel("جودة الفيديو:")
        self.quality_combo = QComboBox()
        self.quality_combo.addItem("عالية", "high")
        self.quality_combo.addItem("متوسطة", "medium")
        self.quality_combo.addItem("منخفضة", "low")
        quality_layout.addWidget(quality_label)
        quality_layout.addWidget(self.quality_combo)

        # إضافة خيار الجودة إلى تخطيط الإعدادات
        settings_layout.addLayout(quality_layout)

        # خيار تقسيم الفيديو
        chunk_layout = QHBoxLayout()
        chunk_check_label = QLabel("تقسيم الفيديو الطويل:")
        self.chunk_check = QCheckBox()
        self.chunk_check.setChecked(True)
        chunk_size_label = QLabel("حجم القطعة (ثانية):")
        self.chunk_size_spin = QSpinBox()
        self.chunk_size_spin.setRange(10, 300)
        self.chunk_size_spin.setValue(30)
        chunk_layout.addWidget(chunk_check_label)
        chunk_layout.addWidget(self.chunk_check)
        chunk_layout.addWidget(chunk_size_label)
        chunk_layout.addWidget(self.chunk_size_spin)

        # إضافة خيار التقسيم إلى تخطيط الإعدادات
        settings_layout.addLayout(chunk_layout)

        # إضافة مجموعة الإعدادات إلى التخطيط الرئيسي
        layout.addWidget(settings_group)

        # إنشاء مجموعة معاينة الدبلجة
        preview_group = QGroupBox("معاينة الدبلجة")
        preview_layout = QVBoxLayout(preview_group)

        # زر معاينة الدبلجة
        self.preview_dub_button = QPushButton("معاينة الدبلجة")
        self.preview_dub_button.setEnabled(False)
        preview_layout.addWidget(self.preview_dub_button)

        # إضافة مجموعة المعاينة إلى التخطيط الرئيسي
        layout.addWidget(preview_group)

        # إنشاء مجموعة الإخراج
        output_group = QGroupBox("إعدادات الإخراج")
        output_layout = QVBoxLayout(output_group)

        # خيار حفظ الترجمات النصية
        self.save_subtitles_check = QCheckBox("حفظ الترجمات النصية مع الفيديو")
        self.save_subtitles_check.setChecked(True)
        output_layout.addWidget(self.save_subtitles_check)

        # خيار حفظ ملف الصوت منفصلاً
        self.save_audio_check = QCheckBox("حفظ ملف الصوت منفصلاً")
        self.save_audio_check.setChecked(False)
        output_layout.addWidget(self.save_audio_check)

        # إضافة مجموعة الإخراج إلى التخطيط الرئيسي
        layout.addWidget(output_group)

        # إضافة مساحة مرنة
        layout.addStretch(1)

    def _connect_signals(self):
        """ربط الإشارات"""
        # ربط أزرار التحكم
        self.extract_button.clicked.connect(self.extract_text)
        self.translate_button.clicked.connect(self.translate_text)
        self.generate_audio_button.clicked.connect(self.generate_audio)
        self.dub_button.clicked.connect(self.dub_video)
        self.full_dub_button.clicked.connect(self.full_dubbing_process)

        # ربط إشارات علامة تبويب الصوت
        self.generate_sample_button.clicked.connect(self.generate_audio_sample)
        self.play_audio_button.clicked.connect(self.play_audio)
        self.stop_audio_button.clicked.connect(self.stop_audio)

        # ربط إشارات علامة تبويب الدبلجة
        self.preview_dub_button.clicked.connect(self.preview_dub)

        # ربط إشارات تغيير الإعدادات
        self.source_language_combo.currentIndexChanged.connect(self.on_language_changed)
        self.target_language_combo.currentIndexChanged.connect(self.on_language_changed)
        self.whisper_model_combo.currentIndexChanged.connect(self.on_model_changed)
        self.tts_model_combo.currentIndexChanged.connect(self.on_model_changed)

        # ربط إشارات التبديل بين علامات التبويب
        self.tab_widget.currentChanged.connect(self.on_tab_changed)

    def apply_settings(self):
        """تطبيق الإعدادات"""
        # تعيين لغة المصدر
        source_lang = self.config.get("translation", "source_language", "auto")
        index = self.source_language_combo.findData(source_lang)
        if index >= 0:
            self.source_language_combo.setCurrentIndex(index)

        # تعيين لغة الهدف
        target_lang = self.config.get("translation", "target_language", "ar")
        index = self.target_language_combo.findData(target_lang)
        if index >= 0:
            self.target_language_combo.setCurrentIndex(index)

        # تعيين نموذج Whisper
        whisper_model = self.config.get("translation", "whisper_model", "medium")
        index = self.whisper_model_combo.findData(whisper_model)
        if index >= 0:
            self.whisper_model_combo.setCurrentIndex(index)

        # تعيين نموذج TTS
        tts_model = self.config.get("translation", "tts_model", "tts_models/ar/fairseq/tts-transformer")
        index = self.tts_model_combo.findData(tts_model)
        if index >= 0:
            self.tts_model_combo.setCurrentIndex(index)

        # تعيين نوع الصوت
        voice_gender = self.config.get("translation", "voice_gender", "male")
        index = self.voice_gender_combo.findData(voice_gender)
        if index >= 0:
            self.voice_gender_combo.setCurrentIndex(index)

        # تعيين سرعة الكلام
        speech_rate = self.config.get("translation", "speech_rate", 1.0)
        self.speech_rate_spin.setValue(speech_rate)

        # تعيين جودة الفيديو
        quality = self.config.get("processing", "quality", "high")
        index = self.quality_combo.findData(quality)
        if index >= 0:
            self.quality_combo.setCurrentIndex(index)

        # تعيين حجم القطعة
        chunk_size = self.config.get("processing", "chunk_size", 30)
        self.chunk_size_spin.setValue(chunk_size)

    def reset(self):
        """إعادة تعيين حالة اللوحة"""
        # إعادة تعيين النصوص
        self.source_text_edit.clear()
        self.translated_text_edit.clear()

        # إعادة تعيين حالة الأزرار
        self.translate_button.setEnabled(False)
        self.generate_audio_button.setEnabled(False)
        self.dub_button.setEnabled(False)
        self.play_audio_button.setEnabled(False)
        self.stop_audio_button.setEnabled(False)
        self.preview_dub_button.setEnabled(False)

        # إعادة تعيين المسارات
        self.extracted_text = ""
        self.translated_text = ""
        self.audio_path = ""
        self.dubbed_video_path = ""

        # إعادة تعيين شريط التقدم
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(False)

    def on_language_changed(self, _):
        """معالجة حدث تغيير اللغة"""
        # تحديث الإعدادات
        source_lang = self.source_language_combo.currentData()
        target_lang = self.target_language_combo.currentData()

        self.config.set("translation", "source_language", source_lang)
        self.config.set("translation", "target_language", target_lang)

    def on_model_changed(self, _):
        """معالجة حدث تغيير النموذج"""
        # تحديث الإعدادات
        whisper_model = self.whisper_model_combo.currentData()
        tts_model = self.tts_model_combo.currentData()

        self.config.set("translation", "whisper_model", whisper_model)
        self.config.set("translation", "tts_model", tts_model)

    def on_tab_changed(self, index):
        """معالجة حدث تغيير علامة التبويب"""
        # تحديث الواجهة بناءً على علامة التبويب المحددة
        if index == 0:  # علامة تبويب الترجمة
            pass
        elif index == 1:  # علامة تبويب الصوت
            pass
        elif index == 2:  # علامة تبويب الدبلجة
            pass

    def extract_text(self, video_path=None):
        """استخراج النص من الفيديو"""
        if self.is_processing:
            return

        if not video_path:
            # إذا لم يتم تمرير مسار الفيديو، فهذا يعني أن المستخدم ضغط على الزر
            # ويجب أن يكون هناك فيديو محمل في النافذة الرئيسية
            self.text_extracted.emit(False)
            return

        # تعيين حالة المعالجة
        self.is_processing = True
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.progress_updated.emit(0, "جاري استخراج النص من الفيديو...")

        # الحصول على إعدادات النموذج
        whisper_model = self.whisper_model_combo.currentData()
        source_lang = self.source_language_combo.currentData()

        # إنشاء خيط منفصل لاستخراج النص
        def extraction_thread():
            try:
                # استخراج النص باستخدام محرك الترجمة
                result = self.translation_engine.extract_text(
                    video_path,
                    model=whisper_model,
                    language=source_lang if source_lang != "auto" else None,
                    progress_callback=self._update_progress
                )

                if result and "text" in result:
                    # تحديث النص المستخرج
                    self.extracted_text = result["text"]

                    # تحديث واجهة المستخدم في الخيط الرئيسي
                    self.source_text_edit.setText(self.extracted_text)

                    # تمكين زر الترجمة
                    self.translate_button.setEnabled(True)

                    # إرسال إشارة النجاح
                    self.text_extracted.emit(True)
                    self.progress_updated.emit(100, "تم استخراج النص بنجاح")
                else:
                    # إرسال إشارة الفشل
                    self.text_extracted.emit(False)
                    self.progress_updated.emit(0, "فشل في استخراج النص")
            except Exception as e:
                # إرسال إشارة الفشل مع رسالة الخطأ
                self.text_extracted.emit(False)
                self.progress_updated.emit(0, f"خطأ في استخراج النص: {str(e)}")
            finally:
                # إعادة تعيين حالة المعالجة
                self.is_processing = False

        # بدء الخيط
        import threading
        extraction_thread = threading.Thread(target=extraction_thread)
        extraction_thread.daemon = True
        extraction_thread.start()

    def translate_text(self):
        """ترجمة النص المستخرج"""
        if self.is_processing or not self.extracted_text:
            return

        # تعيين حالة المعالجة
        self.is_processing = True
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.progress_updated.emit(0, "جاري ترجمة النص...")

        # الحصول على إعدادات الترجمة
        source_lang = self.source_language_combo.currentData()
        target_lang = self.target_language_combo.currentData()

        # الحصول على النص المستخرج (قد يكون المستخدم قد عدله)
        text_to_translate = self.source_text_edit.toPlainText()

        # إنشاء خيط منفصل للترجمة
        def run_translation():
            try:
                # ترجمة النص باستخدام محرك الترجمة
                result = self.translation_engine.translate_text(
                    text_to_translate,
                    source_lang=source_lang,
                    target_lang=target_lang,
                    progress_callback=self._update_progress
                )

                if result and "text" in result:
                    # تحديث النص المترجم
                    self.translated_text = result["text"]

                    # تحديث واجهة المستخدم في الخيط الرئيسي
                    self.translated_text_edit.setText(self.translated_text)

                    # تمكين زر توليد الصوت
                    self.generate_audio_button.setEnabled(True)

                    # إرسال إشارة النجاح
                    self.text_translated.emit(True)
                    self.progress_updated.emit(100, "تم ترجمة النص بنجاح")
                else:
                    # إرسال إشارة الفشل
                    self.text_translated.emit(False)
                    self.progress_updated.emit(0, "فشل في ترجمة النص")
            except Exception as e:
                # إرسال إشارة الفشل مع رسالة الخطأ
                self.text_translated.emit(False)
                self.progress_updated.emit(0, f"خطأ في ترجمة النص: {str(e)}")
            finally:
                # إعادة تعيين حالة المعالجة
                self.is_processing = False

        # بدء الخيط
        import threading
        translation_thread = threading.Thread(target=run_translation)
        translation_thread.daemon = True
        translation_thread.start()

    def generate_audio(self):
        """توليد الصوت من النص المترجم"""
        if self.is_processing or not self.translated_text:
            return

        # تعيين حالة المعالجة
        self.is_processing = True
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.progress_updated.emit(0, "جاري توليد الصوت...")

        # الحصول على إعدادات توليد الصوت
        tts_model = self.tts_model_combo.currentData()
        voice_gender = self.voice_gender_combo.currentData()
        speech_rate = self.speech_rate_spin.value()
        target_lang = self.target_language_combo.currentData()

        # الحصول على النص المترجم (قد يكون المستخدم قد عدله)
        text_for_tts = self.translated_text_edit.toPlainText()

        # إنشاء خيط منفصل لتوليد الصوت
        def run_tts():
            try:
                # توليد الصوت باستخدام محرك TTS
                result = self.tts_engine.generate_speech(
                    text_for_tts,
                    model=tts_model,
                    language=target_lang,
                    gender=voice_gender,
                    speed=speech_rate,
                    enhance=self.enhance_audio_check.isChecked(),
                    progress_callback=self._update_progress
                )

                if result and "audio_path" in result:
                    # تحديث مسار الصوت
                    self.audio_path = result["audio_path"]

                    # تمكين أزرار التحكم في الصوت
                    self.play_audio_button.setEnabled(True)
                    self.stop_audio_button.setEnabled(True)
                    self.dub_button.setEnabled(True)
                    self.preview_dub_button.setEnabled(True)

                    # إرسال إشارة النجاح
                    self.audio_generated.emit(True)
                    self.progress_updated.emit(100, "تم توليد الصوت بنجاح")
                else:
                    # إرسال إشارة الفشل
                    self.audio_generated.emit(False)
                    self.progress_updated.emit(0, "فشل في توليد الصوت")
            except Exception as e:
                # إرسال إشارة الفشل مع رسالة الخطأ
                self.audio_generated.emit(False)
                self.progress_updated.emit(0, f"خطأ في توليد الصوت: {str(e)}")
            finally:
                # إعادة تعيين حالة المعالجة
                self.is_processing = False

        # بدء الخيط
        import threading
        tts_thread = threading.Thread(target=run_tts)
        tts_thread.daemon = True
        tts_thread.start()

    def generate_audio_sample(self):
        """توليد عينة صوتية"""
        if self.is_processing:
            return

        # تعيين حالة المعالجة
        self.is_processing = True
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.progress_updated.emit(0, "جاري توليد عينة صوتية...")

        # الحصول على إعدادات توليد الصوت
        tts_model = self.tts_model_combo.currentData()
        voice_gender = self.voice_gender_combo.currentData()
        speech_rate = self.speech_rate_spin.value()
        target_lang = self.target_language_combo.currentData()

        # إنشاء نص قصير للعينة
        sample_text = "هذا مثال على الصوت المولد. يمكنك ضبط الإعدادات لتغيير نبرة وسرعة الصوت."
        if target_lang == "en":
            sample_text = "This is an example of the generated voice. You can adjust the settings to change the tone and speed of the voice."

        # إنشاء خيط منفصل لتوليد العينة الصوتية
        def run_sample_tts():
            try:
                # توليد الصوت باستخدام محرك TTS
                result = self.tts_engine.generate_speech(
                    sample_text,
                    model=tts_model,
                    language=target_lang,
                    gender=voice_gender,
                    speed=speech_rate,
                    enhance=self.enhance_audio_check.isChecked(),
                    is_sample=True,
                    progress_callback=self._update_progress
                )

                if result and "audio_path" in result:
                    # تحديث مسار الصوت
                    self.audio_path = result["audio_path"]

                    # تمكين أزرار التحكم في الصوت
                    self.play_audio_button.setEnabled(True)
                    self.stop_audio_button.setEnabled(True)

                    # تشغيل العينة تلقائيًا
                    self.play_audio()

                    # إرسال إشارة النجاح
                    self.progress_updated.emit(100, "تم توليد العينة الصوتية بنجاح")
                else:
                    # إرسال إشارة الفشل
                    self.progress_updated.emit(0, "فشل في توليد العينة الصوتية")
            except Exception as e:
                # إرسال إشارة الفشل مع رسالة الخطأ
                self.progress_updated.emit(0, f"خطأ في توليد العينة الصوتية: {str(e)}")
            finally:
                # إعادة تعيين حالة المعالجة
                self.is_processing = False

        # بدء الخيط
        import threading
        sample_thread = threading.Thread(target=run_sample_tts)
        sample_thread.daemon = True
        sample_thread.start()

    def play_audio(self):
        """تشغيل الصوت المولد"""
        if not self.audio_path or not os.path.exists(self.audio_path):
            return

        # إنشاء مشغل الوسائط إذا لم يكن موجودًا
        if not hasattr(self, "media_player"):
            self.media_player = QMediaPlayer()
            self.media_player.stateChanged.connect(self._on_audio_state_changed)

        # تحميل ملف الصوت
        self.media_player.setMedia(QMediaContent(QUrl.fromLocalFile(self.audio_path)))

        # تشغيل الصوت
        self.media_player.play()

    def stop_audio(self):
        """إيقاف تشغيل الصوت"""
        if hasattr(self, "media_player"):
            self.media_player.stop()

    def _on_audio_state_changed(self, state):
        """معالجة حدث تغيير حالة تشغيل الصوت"""
        if state == QMediaPlayer.PlayingState:
            self.play_audio_button.setEnabled(False)
            self.stop_audio_button.setEnabled(True)
        else:
            self.play_audio_button.setEnabled(True)
            self.stop_audio_button.setEnabled(False)

    def preview_dub(self):
        """معاينة الدبلجة"""
        # هذه الدالة ستنفذ في الإصدار النهائي
        pass

    def dub_video(self, video_path=None):
        """دبلجة الفيديو"""
        if self.is_processing or not self.audio_path:
            return

        if not video_path:
            # إذا لم يتم تمرير مسار الفيديو، فهذا يعني أن المستخدم ضغط على الزر
            # ويجب أن يكون هناك فيديو محمل في النافذة الرئيسية
            self.video_dubbed.emit(False, "")
            return

        # تعيين حالة المعالجة
        self.is_processing = True
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.progress_updated.emit(0, "جاري دبلجة الفيديو...")

        # الحصول على إعدادات الدبلجة
        mute_original = self.mute_original_check.isChecked()
        lower_original = self.lower_original_check.isChecked()
        quality = self.quality_combo.currentData()
        use_chunks = self.chunk_check.isChecked()
        chunk_size = self.chunk_size_spin.value()
        save_subtitles = self.save_subtitles_check.isChecked()

        # إنشاء خيط منفصل للدبلجة
        def run_dubbing():
            try:
                # دبلجة الفيديو باستخدام محرك المعالجة
                from core.media_processor import MediaProcessor
                media_processor = MediaProcessor(self.config)

                result = media_processor.dub_video(
                    video_path=video_path,
                    audio_path=self.audio_path,
                    mute_original=mute_original,
                    lower_original=lower_original,
                    quality=quality,
                    use_chunks=use_chunks,
                    chunk_size=chunk_size,
                    save_subtitles=save_subtitles,
                    progress_callback=self._update_progress
                )

                if result and "output_path" in result:
                    # تحديث مسار الفيديو المدبلج
                    self.dubbed_video_path = result["output_path"]

                    # إرسال إشارة النجاح
                    self.video_dubbed.emit(True, self.dubbed_video_path)
                    self.progress_updated.emit(100, "تم دبلجة الفيديو بنجاح")
                else:
                    # إرسال إشارة الفشل
                    self.video_dubbed.emit(False, "")
                    self.progress_updated.emit(0, "فشل في دبلجة الفيديو")
            except Exception as e:
                # إرسال إشارة الفشل مع رسالة الخطأ
                self.video_dubbed.emit(False, "")
                self.progress_updated.emit(0, f"خطأ في دبلجة الفيديو: {str(e)}")
            finally:
                # إعادة تعيين حالة المعالجة
                self.is_processing = False

        # بدء الخيط
        import threading
        dubbing_thread = threading.Thread(target=run_dubbing)
        dubbing_thread.daemon = True
        dubbing_thread.start()

    def _update_progress(self, progress, message=""):
        """تحديث التقدم"""
        # إرسال إشارة تحديث التقدم
        self.progress_updated.emit(progress, message)

    def full_dubbing_process(self, video_path=None):
        """عملية الدبلجة الشاملة - كل شيء في خطوة واحدة"""
        if self.is_processing:
            return

        if not video_path:
            # إذا لم يتم تمرير مسار الفيديو، فهذا يعني أن المستخدم ضغط على الزر
            # ويجب أن يكون هناك فيديو محمل في النافذة الرئيسية
            self.video_dubbed.emit(False, "")
            return

        # تعيين حالة المعالجة
        self.is_processing = True
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.progress_updated.emit(0, "جاري بدء عملية الدبلجة الشاملة...")

        # تعطيل جميع الأزرار
        self.extract_button.setEnabled(False)
        self.translate_button.setEnabled(False)
        self.generate_audio_button.setEnabled(False)
        self.dub_button.setEnabled(False)
        self.full_dub_button.setEnabled(False)

        # إنشاء خيط منفصل للدبلجة الشاملة
        def run_full_dubbing():
            try:
                # المرحلة 1: استخراج النص
                self.progress_updated.emit(5, "المرحلة 1/4: جاري استخراج النص من الفيديو...")

                whisper_model = self.whisper_model_combo.currentData()
                source_lang = self.source_language_combo.currentData()

                result = self.translation_engine.extract_text(
                    video_path,
                    model=whisper_model,
                    language=source_lang if source_lang != "auto" else None,
                    progress_callback=lambda p, m: self.progress_updated.emit(5 + p * 0.2, f"المرحلة 1/4: {m}")
                )

                if not result or "text" not in result:
                    self.progress_updated.emit(0, "فشل في استخراج النص")
                    return

                extracted_text = result["text"]
                self.source_text_edit.setText(extracted_text)
                self.progress_updated.emit(25, "تم استخراج النص بنجاح")

                # المرحلة 2: ترجمة النص
                self.progress_updated.emit(25, "المرحلة 2/4: جاري ترجمة النص...")

                source_lang = self.source_language_combo.currentData()
                target_lang = self.target_language_combo.currentData()

                translation_result = self.translation_engine.translate_text(
                    extracted_text,
                    source_lang=source_lang,
                    target_lang=target_lang,
                    progress_callback=lambda p, m: self.progress_updated.emit(25 + p * 0.25, f"المرحلة 2/4: {m}")
                )

                if not translation_result or "text" not in translation_result:
                    self.progress_updated.emit(0, "فشل في ترجمة النص")
                    return

                translated_text = translation_result["text"]
                self.translated_text_edit.setText(translated_text)
                self.progress_updated.emit(50, "تم ترجمة النص بنجاح")

                # المرحلة 3: توليد الصوت
                self.progress_updated.emit(50, "المرحلة 3/4: جاري توليد الصوت...")

                tts_model = self.tts_model_combo.currentData()
                voice_gender = self.voice_gender_combo.currentData()
                speech_rate = self.speech_rate_spin.value()

                audio_result = self.tts_engine.generate_speech(
                    translated_text,
                    model=tts_model,
                    language=target_lang,
                    gender=voice_gender,
                    speed=speech_rate,
                    enhance=self.enhance_audio_check.isChecked(),
                    progress_callback=lambda p, m: self.progress_updated.emit(50 + p * 0.25, f"المرحلة 3/4: {m}")
                )

                if not audio_result or "audio_path" not in audio_result:
                    self.progress_updated.emit(0, "فشل في توليد الصوت")
                    return

                audio_path = audio_result["audio_path"]
                self.progress_updated.emit(75, "تم توليد الصوت بنجاح")

                # المرحلة 4: دبلجة الفيديو
                self.progress_updated.emit(75, "المرحلة 4/4: جاري دبلجة الفيديو...")

                # استخدام محرك الدبلجة الجديد
                from core.dubbing_engine import DubbingEngine
                dubbing_engine = DubbingEngine(self.config)

                # تحديد مسار الإخراج
                import os
                import tempfile
                output_dir = os.path.dirname(video_path)
                base_name = os.path.splitext(os.path.basename(video_path))[0]
                output_path = os.path.join(output_dir, f"{base_name}_dubbed.mp4")

                # إعدادات الدبلجة
                mix_original = not self.mute_original_check.isChecked()
                original_volume = 0.3 if self.lower_original_check.isChecked() else 1.0

                dubbed_video_path = dubbing_engine.dub_video(
                    input_video=video_path,
                    audio_track=audio_path,
                    output_video=output_path,
                    mix_original=mix_original,
                    original_volume=original_volume,
                    progress_callback=lambda p, m: self.progress_updated.emit(75 + p * 0.25, f"المرحلة 4/4: {m}")
                )

                if dubbed_video_path:
                    self.dubbed_video_path = dubbed_video_path
                    self.progress_updated.emit(100, "تمت الدبلجة الشاملة بنجاح!")
                    self.video_dubbed.emit(True, dubbed_video_path)
                else:
                    self.progress_updated.emit(0, "فشل في دبلجة الفيديو")
                    self.video_dubbed.emit(False, "")

            except Exception as e:
                self.progress_updated.emit(0, f"خطأ في الدبلجة الشاملة: {str(e)}")
                self.video_dubbed.emit(False, "")
            finally:
                # إعادة تمكين الأزرار
                self.extract_button.setEnabled(True)
                self.full_dub_button.setEnabled(True)
                # إعادة تعيين حالة المعالجة
                self.is_processing = False

        # بدء الخيط
        import threading
        full_dubbing_thread = threading.Thread(target=run_full_dubbing)
        full_dubbing_thread.daemon = True
        full_dubbing_thread.start()
