#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
النافذة الرئيسية لمشغل الوسائط مع الدبلجة الآلية
"""

import os
import sys
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QPushButton, QSlider, QLabel, QFileDialog,
                            QAction, QMenu, QToolBar, QDockWidget, QTabWidget,
                            QMessageBox, QComboBox, QSpinBox, QDoubleSpinBox,
                            QCheckBox, QGroupBox, QRadioButton, QProgressBar,
                            QSplitter, QListWidget, QTextEdit, QLineEdit, QStyle)
from PyQt5.QtCore import Qt, QUrl, QTimer, QSize, QThread, pyqtSignal, QSettings
from PyQt5.QtGui import QIcon, QKeySequence, QFont
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtMultimediaWidgets import QVideoWidget

# استخدام مشغل الفيديو البسيط والموثوق
from ui.video_player import VideoPlayer
print("✅ استخدام مشغل الفيديو البسيط والموثوق")
from ui.translation_panel import TranslationPanel
from ui.settings_dialog import SettingsDialog
from ui.about_dialog import AboutDialog
from ui.download_dialog import DownloadDialog

from core.simple_media_processor import SimpleMediaProcessor
from core.translation_engine import TranslationEngine
from core.tts_engine import TTSEngine

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""

    def __init__(self, config):
        """تهيئة النافذة الرئيسية"""
        super().__init__()

        self.config = config
        self.media_processor = SimpleMediaProcessor(config)
        self.translation_engine = TranslationEngine(config)
        self.tts_engine = TTSEngine(config)

        self.current_file = None
        self.is_processing = False

        self._init_ui()
        self._create_actions()
        self._create_menus()
        self._create_toolbars()
        self._create_statusbar()
        self._connect_signals()

        # تحميل الإعدادات
        self._load_settings()

        # التحقق من وجود النماذج المطلوبة
        self._check_models()

    def _init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("مشغل الوسائط مع الدبلجة الآلية")
        self.setMinimumSize(1000, 600)

        # إنشاء الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)

        # إنشاء مقسم لتقسيم الشاشة بين مشغل الفيديو ولوحة الترجمة
        self.splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(self.splitter)

        # إنشاء مشغل الفيديو
        self.video_player = VideoPlayer(self.config)
        self.splitter.addWidget(self.video_player)

        # إنشاء لوحة الترجمة
        self.translation_panel = TranslationPanel(self.config, self.translation_engine, self.tts_engine)
        self.splitter.addWidget(self.translation_panel)

        # تعيين نسب المقسم
        self.splitter.setSizes([700, 300])

        # تعيين نمط النافذة
        self._set_theme()

    def _create_actions(self):
        """إنشاء الإجراءات"""
        # إجراءات الملف
        self.open_action = QAction(self.style().standardIcon(QStyle.SP_DialogOpenButton), "فتح ملف", self)
        self.open_action.setShortcut(QKeySequence.Open)
        self.open_action.triggered.connect(self.open_file)

        self.save_action = QAction(self.style().standardIcon(QStyle.SP_DialogSaveButton), "حفظ الفيديو المدبلج", self)
        self.save_action.setShortcut(QKeySequence.Save)
        self.save_action.triggered.connect(self.save_file)
        self.save_action.setEnabled(False)

        self.exit_action = QAction(self.style().standardIcon(QStyle.SP_DialogCloseButton), "خروج", self)
        self.exit_action.setShortcut(QKeySequence.Quit)
        self.exit_action.triggered.connect(self.close)

        # إجراءات التشغيل
        self.play_action = QAction(self.style().standardIcon(QStyle.SP_MediaPlay), "تشغيل", self)
        self.play_action.setShortcut(Qt.Key_Space)
        self.play_action.triggered.connect(self.video_player.toggle_play)

        self.stop_action = QAction(self.style().standardIcon(QStyle.SP_MediaStop), "إيقاف", self)
        self.stop_action.triggered.connect(self.video_player.stop)

        self.mute_action = QAction(self.style().standardIcon(QStyle.SP_MediaVolume), "كتم الصوت", self)
        self.mute_action.triggered.connect(self.video_player.toggle_mute)

        self.fullscreen_action = QAction(self.style().standardIcon(QStyle.SP_TitleBarMaxButton), "ملء الشاشة", self)
        self.fullscreen_action.setShortcut(Qt.Key_F)
        self.fullscreen_action.triggered.connect(self.video_player.toggle_fullscreen)

        # إجراءات الترجمة
        self.extract_action = QAction(QIcon(), "استخراج النص", self)
        self.extract_action.triggered.connect(self.extract_text)
        self.extract_action.setEnabled(False)

        self.translate_action = QAction(QIcon(), "ترجمة النص", self)
        self.translate_action.triggered.connect(self.translate_text)
        self.translate_action.setEnabled(False)

        self.generate_audio_action = QAction(QIcon(), "توليد الصوت", self)
        self.generate_audio_action.triggered.connect(self.generate_audio)
        self.generate_audio_action.setEnabled(False)

        self.dub_action = QAction(QIcon(), "دبلجة الفيديو", self)
        self.dub_action.triggered.connect(self.dub_video)
        self.dub_action.setEnabled(False)

        self.full_dub_action = QAction(QIcon(), "دبلجة شاملة", self)
        self.full_dub_action.triggered.connect(self.full_dubbing_process)
        self.full_dub_action.setEnabled(False)

        # إجراءات أخرى
        self.settings_action = QAction(self.style().standardIcon(QStyle.SP_FileDialogDetailedView), "الإعدادات", self)
        self.settings_action.triggered.connect(self.show_settings)

        self.about_action = QAction(self.style().standardIcon(QStyle.SP_FileDialogInfoView), "حول البرنامج", self)
        self.about_action.triggered.connect(self.show_about)

    def _create_menus(self):
        """إنشاء القوائم"""
        # قائمة الملف
        file_menu = self.menuBar().addMenu("ملف")
        file_menu.addAction(self.open_action)
        file_menu.addAction(self.save_action)
        file_menu.addSeparator()

        # قائمة الملفات الأخيرة
        self.recent_menu = QMenu("الملفات الأخيرة", self)
        file_menu.addMenu(self.recent_menu)
        self._update_recent_files_menu()

        file_menu.addSeparator()
        file_menu.addAction(self.exit_action)

        # قائمة التشغيل
        playback_menu = self.menuBar().addMenu("تشغيل")
        playback_menu.addAction(self.play_action)
        playback_menu.addAction(self.stop_action)
        playback_menu.addAction(self.mute_action)
        playback_menu.addSeparator()
        playback_menu.addAction(self.fullscreen_action)

        # قائمة الترجمة
        translation_menu = self.menuBar().addMenu("ترجمة")
        translation_menu.addAction(self.extract_action)
        translation_menu.addAction(self.translate_action)
        translation_menu.addAction(self.generate_audio_action)
        translation_menu.addAction(self.dub_action)
        translation_menu.addSeparator()
        translation_menu.addAction(self.full_dub_action)

        # قائمة المساعدة
        help_menu = self.menuBar().addMenu("مساعدة")
        help_menu.addAction(self.settings_action)
        help_menu.addSeparator()
        help_menu.addAction(self.about_action)

    def _create_toolbars(self):
        """إنشاء أشرطة الأدوات"""
        # شريط أدوات الملف
        file_toolbar = self.addToolBar("ملف")
        file_toolbar.setMovable(False)
        file_toolbar.addAction(self.open_action)
        file_toolbar.addAction(self.save_action)

        # شريط أدوات التشغيل
        playback_toolbar = self.addToolBar("تشغيل")
        playback_toolbar.setMovable(False)
        playback_toolbar.addAction(self.play_action)
        playback_toolbar.addAction(self.stop_action)
        playback_toolbar.addAction(self.mute_action)
        playback_toolbar.addAction(self.fullscreen_action)

        # شريط أدوات الترجمة
        translation_toolbar = self.addToolBar("ترجمة")
        translation_toolbar.setMovable(False)
        translation_toolbar.addAction(self.extract_action)
        translation_toolbar.addAction(self.translate_action)
        translation_toolbar.addAction(self.generate_audio_action)
        translation_toolbar.addAction(self.dub_action)

    def _create_statusbar(self):
        """إنشاء شريط الحالة"""
        self.statusBar().showMessage("جاهز")

        # إضافة مؤشر التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setMaximumWidth(150)
        self.progress_bar.setVisible(False)
        self.statusBar().addPermanentWidget(self.progress_bar)

    def _connect_signals(self):
        """ربط الإشارات"""
        # ربط إشارات مشغل الفيديو
        self.video_player.media_loaded.connect(self._on_media_loaded)
        self.video_player.playback_state_changed.connect(self._on_playback_state_changed)

        # ربط إشارات لوحة الترجمة
        self.translation_panel.text_extracted.connect(self._on_text_extracted)
        self.translation_panel.text_translated.connect(self._on_text_translated)
        self.translation_panel.audio_generated.connect(self._on_audio_generated)
        self.translation_panel.video_dubbed.connect(self._on_video_dubbed)
        self.translation_panel.progress_updated.connect(self._on_progress_updated)

    def _load_settings(self):
        """تحميل إعدادات النافذة"""
        settings = QSettings("MediaTranslator", "MainWindow")

        # استعادة حجم وموضع النافذة
        if settings.contains("geometry"):
            self.restoreGeometry(settings.value("geometry"))

        # استعادة حالة النافذة
        if settings.contains("windowState"):
            self.restoreState(settings.value("windowState"))

        # استعادة أحجام المقسم
        if settings.contains("splitterSizes"):
            # تحويل القيم إلى أرقام صحيحة
            try:
                sizes_value = settings.value("splitterSizes")
                if isinstance(sizes_value, list):
                    sizes = [int(size) if isinstance(size, str) else size for size in sizes_value]
                    self.splitter.setSizes(sizes)
                else:
                    # تعيين القيم الافتراضية إذا لم تكن القيمة قائمة
                    self.splitter.setSizes([700, 300])
            except Exception:
                # في حالة حدوث أي خطأ، استخدم القيم الافتراضية
                self.splitter.setSizes([700, 300])

    def _save_settings(self):
        """حفظ إعدادات النافذة"""
        settings = QSettings("MediaTranslator", "MainWindow")

        # حفظ حجم وموضع النافذة
        settings.setValue("geometry", self.saveGeometry())

        # حفظ حالة النافذة
        settings.setValue("windowState", self.saveState())

        # حفظ أحجام المقسم
        settings.setValue("splitterSizes", self.splitter.sizes())

    def _set_theme(self):
        """تعيين سمة التطبيق"""
        theme = self.config.get("general", "theme", "dark")

        if theme == "dark":
            # تطبيق السمة الداكنة
            self.setStyleSheet("""
                QMainWindow, QDialog {
                    background-color: #2D2D30;
                    color: #FFFFFF;
                }
                QWidget {
                    background-color: #2D2D30;
                    color: #FFFFFF;
                }
                QMenuBar, QMenu {
                    background-color: #1E1E1E;
                    color: #FFFFFF;
                }
                QMenuBar::item:selected, QMenu::item:selected {
                    background-color: #3E3E40;
                }
                QToolBar {
                    background-color: #1E1E1E;
                    border: none;
                }
                QPushButton {
                    background-color: #0E639C;
                    color: #FFFFFF;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 2px;
                }
                QPushButton:hover {
                    background-color: #1177BB;
                }
                QPushButton:pressed {
                    background-color: #0D5A8C;
                }
                QPushButton:disabled {
                    background-color: #3E3E40;
                    color: #9D9D9D;
                }
                QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                    background-color: #1E1E1E;
                    color: #FFFFFF;
                    border: 1px solid #3E3E40;
                    padding: 2px;
                }
                QTabWidget::pane {
                    border: 1px solid #3E3E40;
                }
                QTabBar::tab {
                    background-color: #2D2D30;
                    color: #FFFFFF;
                    padding: 5px 10px;
                    border: 1px solid #3E3E40;
                    border-bottom: none;
                }
                QTabBar::tab:selected {
                    background-color: #0E639C;
                }
                QProgressBar {
                    border: 1px solid #3E3E40;
                    border-radius: 2px;
                    text-align: center;
                }
                QProgressBar::chunk {
                    background-color: #0E639C;
                }
            """)
        else:
            # تطبيق السمة الفاتحة
            self.setStyleSheet("")

    def _check_models(self):
        """التحقق من وجود النماذج المطلوبة مع عرض تفصيلي"""
        print("\n" + "="*60)
        print("🔍 فحص النماذج عند بدء التشغيل")
        print("="*60)

        # فحص جميع النماذج
        all_models_available = self.media_processor.check_all_models()

        if not all_models_available:
            print("\n" + "="*60)

            # عرض رسالة للمستخدم
            reply = QMessageBox.question(
                self, "نماذج مفقودة",
                "تم العثور على نماذج مفقودة.\n\n"
                "هل تريد تنزيل النماذج المفقودة الآن؟\n\n"
                "ملاحظة: قد يستغرق التنزيل بعض الوقت حسب سرعة الإنترنت.\n"
                "يمكنك أيضاً تنزيلها لاحقاً باستخدام download_models.bat",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                # عرض رسالة تنبيه عن التنزيل
                QMessageBox.information(
                    self, "بدء التنزيل",
                    "سيتم تنزيل النماذج المفقودة تلقائياً عند استخدام الوظائف.\n\n"
                    "للتنزيل المسبق لجميع النماذج، استخدم ملف download_models.bat",
                    QMessageBox.Ok
                )
            else:
                # عرض رسالة عن كيفية التنزيل لاحقاً
                QMessageBox.information(
                    self, "تنزيل لاحقاً",
                    "يمكنك تنزيل النماذج لاحقاً بإحدى الطرق التالية:\n\n"
                    "1. شغل ملف download_models.bat\n"
                    "2. استخدم الوظائف في البرنامج وسيتم التنزيل تلقائياً\n"
                    "3. شغل python download_all_models.py",
                    QMessageBox.Ok
                )
        else:
            print("✅ جميع النماذج متاحة - البرنامج جاهز للاستخدام!")

        print("="*60 + "\n")

    def _download_whisper_model(self, model_name):
        """تنزيل نموذج Whisper"""
        download_dialog = DownloadDialog(self, "تنزيل نموذج Whisper", model_name)
        download_dialog.start_download(self.media_processor.download_whisper_model, model_name)

    def _download_tts_model(self, model_name):
        """تنزيل نموذج TTS"""
        download_dialog = DownloadDialog(self, "تنزيل نموذج TTS", model_name)
        download_dialog.start_download(self.tts_engine.download_tts_model, model_name)

    def _update_recent_files_menu(self):
        """تحديث قائمة الملفات الأخيرة"""
        self.recent_menu.clear()

        recent_files = self.config.get("general", "recent_files", [])

        if not recent_files:
            no_files_action = QAction("لا توجد ملفات حديثة", self)
            no_files_action.setEnabled(False)
            self.recent_menu.addAction(no_files_action)
            return

        for i, file_path in enumerate(recent_files):
            if os.path.exists(file_path):
                action = QAction(f"{i+1}. {os.path.basename(file_path)}", self)
                action.setData(file_path)
                action.triggered.connect(lambda checked, path=file_path: self.open_file(path))
                self.recent_menu.addAction(action)

        self.recent_menu.addSeparator()
        clear_action = QAction("مسح القائمة", self)
        clear_action.triggered.connect(self._clear_recent_files)
        self.recent_menu.addAction(clear_action)

    def _clear_recent_files(self):
        """مسح قائمة الملفات الأخيرة"""
        self.config.set("general", "recent_files", [])
        self._update_recent_files_menu()

    def _on_media_loaded(self, success):
        """معالجة حدث تحميل الوسائط"""
        if success:
            self.statusBar().showMessage(f"✅ تم تحميل الملف: {os.path.basename(self.current_file)}")
            self.extract_action.setEnabled(True)
            self.full_dub_action.setEnabled(True)  # تمكين الدبلجة الشاملة
            print(f"🎉 نجح تحميل الفيديو: {os.path.basename(self.current_file)}")
        else:
            self.statusBar().showMessage("❌ فشل في تحميل الملف")
            self.extract_action.setEnabled(False)
            self.full_dub_action.setEnabled(False)

            # عرض رسالة بسيطة للمستخدم
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(
                self, "تنبيه",
                "لم يتم تحميل ملف الفيديو.\n\n"
                "💡 جرب:\n"
                "• انسخ الملف لمجلد بسيط مثل C:\\Videos\\\n"
                "• تأكد من أن الملف بصيغة MP4\n"
                "• شغل اختبار_الفيديو.bat للمساعدة"
            )

    def _on_playback_state_changed(self, is_playing):
        """معالجة حدث تغيير حالة التشغيل"""
        if is_playing:
            self.play_action.setIcon(self.style().standardIcon(QStyle.SP_MediaPause))
            self.play_action.setText("إيقاف مؤقت")
        else:
            self.play_action.setIcon(self.style().standardIcon(QStyle.SP_MediaPlay))
            self.play_action.setText("تشغيل")

    def _on_text_extracted(self, success):
        """معالجة حدث استخراج النص"""
        if success:
            self.statusBar().showMessage("تم استخراج النص بنجاح")
            self.translate_action.setEnabled(True)
        else:
            self.statusBar().showMessage("فشل في استخراج النص")
            self.translate_action.setEnabled(False)

    def _on_text_translated(self, success):
        """معالجة حدث ترجمة النص"""
        if success:
            self.statusBar().showMessage("تم ترجمة النص بنجاح")
            self.generate_audio_action.setEnabled(True)
        else:
            self.statusBar().showMessage("فشل في ترجمة النص")
            self.generate_audio_action.setEnabled(False)

    def _on_audio_generated(self, success):
        """معالجة حدث توليد الصوت"""
        if success:
            self.statusBar().showMessage("تم توليد الصوت بنجاح")
            self.dub_action.setEnabled(True)
        else:
            self.statusBar().showMessage("فشل في توليد الصوت")
            self.dub_action.setEnabled(False)

    def _on_video_dubbed(self, success, output_path=None):
        """معالجة حدث دبلجة الفيديو"""
        if success and output_path:
            self.statusBar().showMessage(f"تم دبلجة الفيديو بنجاح: {os.path.basename(output_path)}")
            self.save_action.setEnabled(True)

            # عرض رسالة نجاح مع خيار فتح الفيديو المدبلج
            reply = QMessageBox.question(
                self, "تمت الدبلجة بنجاح",
                "تم دبلجة الفيديو بنجاح. هل تريد تشغيل الفيديو المدبلج؟",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                self.open_file(output_path)
        else:
            self.statusBar().showMessage("فشل في دبلجة الفيديو")
            self.save_action.setEnabled(False)

    def _on_progress_updated(self, progress, message):
        """معالجة حدث تحديث التقدم"""
        self.progress_bar.setValue(progress)
        self.progress_bar.setVisible(progress > 0 and progress < 100)

        if message:
            self.statusBar().showMessage(message)

    def open_file(self, file_path=None):
        """فتح ملف فيديو"""
        if not file_path:
            # فتح مربع حوار اختيار الملف
            file_path, _ = QFileDialog.getOpenFileName(
                self, "فتح ملف فيديو",
                self.config.get("paths", "last_open_dir", str(os.path.expanduser("~"))),
                "ملفات الفيديو (*.mp4 *.avi *.mkv *.mov *.wmv *.flv *.webm *.m4v *.3gp);;ملفات MP4 (*.mp4);;ملفات AVI (*.avi);;جميع الملفات (*.*)"
            )

            if not file_path:
                return

        # حفظ مسار الملف
        self.current_file = file_path
        self.config.set("paths", "last_open_dir", os.path.dirname(file_path))
        self.config.add_recent_file(file_path)
        self._update_recent_files_menu()

        # تحميل الفيديو في المشغل
        self.video_player.load_media(file_path)

        # إعادة تعيين حالة الترجمة
        self.translation_panel.reset()
        self.translate_action.setEnabled(False)
        self.generate_audio_action.setEnabled(False)
        self.dub_action.setEnabled(False)
        self.save_action.setEnabled(False)

    def save_file(self):
        """حفظ الفيديو المدبلج"""
        if not hasattr(self.translation_panel, "dubbed_video_path") or not self.translation_panel.dubbed_video_path:
            QMessageBox.warning(self, "خطأ", "لا يوجد فيديو مدبلج للحفظ")
            return

        # فتح مربع حوار حفظ الملف
        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ الفيديو المدبلج",
            self.config.get("paths", "last_save_dir", str(os.path.expanduser("~"))),
            "ملفات الفيديو (*.mp4)"
        )

        if not file_path:
            return

        # إضافة امتداد .mp4 إذا لم يكن موجودًا
        if not file_path.lower().endswith(".mp4"):
            file_path += ".mp4"

        # حفظ مسار الحفظ
        self.config.set("paths", "last_save_dir", os.path.dirname(file_path))

        # نسخ الفيديو المدبلج إلى المسار المحدد
        try:
            import shutil
            shutil.copy2(self.translation_panel.dubbed_video_path, file_path)
            self.statusBar().showMessage(f"تم حفظ الفيديو المدبلج بنجاح: {os.path.basename(file_path)}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الفيديو: {str(e)}")

    def extract_text(self):
        """استخراج النص من الفيديو"""
        if not self.current_file:
            QMessageBox.warning(self, "خطأ", "يرجى فتح ملف فيديو أولاً")
            return

        self.translation_panel.extract_text(self.current_file)

    def translate_text(self):
        """ترجمة النص المستخرج"""
        self.translation_panel.translate_text()

    def generate_audio(self):
        """توليد الصوت من النص المترجم"""
        self.translation_panel.generate_audio()

    def dub_video(self):
        """دبلجة الفيديو"""
        if not self.current_file:
            QMessageBox.warning(self, "خطأ", "يرجى فتح ملف فيديو أولاً")
            return

        self.translation_panel.dub_video(self.current_file)

    def full_dubbing_process(self):
        """عملية الدبلجة الشاملة"""
        if not self.current_file:
            QMessageBox.warning(self, "تحذير", "يرجى فتح ملف فيديو أولاً")
            return

        # التحقق من وجود النماذج المطلوبة
        self._check_models()

        # بدء عملية الدبلجة الشاملة
        self.translation_panel.full_dubbing_process(self.current_file)

    def show_settings(self):
        """عرض نافذة الإعدادات"""
        settings_dialog = SettingsDialog(self.config, self)
        if settings_dialog.exec_():
            # تحديث الإعدادات
            self._set_theme()
            self.video_player.apply_settings()
            self.translation_panel.apply_settings()

    def show_about(self):
        """عرض نافذة حول البرنامج"""
        about_dialog = AboutDialog(self)
        about_dialog.exec_()

    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        # حفظ الإعدادات
        self._save_settings()

        # قبول الحدث لإغلاق النافذة
        event.accept()
