#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة إدارة الإعدادات
"""

import os
import json
from collections import defaultdict

class Config:
    """فئة إدارة الإعدادات"""

    def __init__(self, config_file=None):
        """تهيئة الإعدادات"""
        # تحديد ملف الإعدادات
        if config_file is None:
            # استخدام المسار الافتراضي
            home_dir = os.path.expanduser("~")
            config_dir = os.path.join(home_dir, ".media_translator")

            # إنشاء مجلد الإعدادات إذا لم يكن موجودًا
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)

            self.config_file = os.path.join(config_dir, "config.json")
        else:
            self.config_file = config_file

        # تهيئة الإعدادات
        self.config = defaultdict(dict)

        # تحميل الإعدادات
        self.load()

        # تعيين الإعدادات الافتراضية إذا لم تكن موجودة
        self._set_defaults()

    def _set_defaults(self):
        """تعيين الإعدادات الافتراضية"""
        # الإعدادات العامة
        if "general" not in self.config:
            self.config["general"] = {}

        if "theme" not in self.config["general"]:
            self.config["general"]["theme"] = "dark"

        if "recent_files" not in self.config["general"]:
            self.config["general"]["recent_files"] = []

        # إعدادات المسارات
        if "paths" not in self.config:
            self.config["paths"] = {}

        if "models_dir" not in self.config["paths"]:
            # استخدام مجلد النماذج في مجلد الإعدادات
            home_dir = os.path.expanduser("~")
            models_dir = os.path.join(home_dir, ".media_translator", "models")

            # إنشاء مجلد النماذج إذا لم يكن موجودًا
            if not os.path.exists(models_dir):
                os.makedirs(models_dir)

            self.config["paths"]["models_dir"] = models_dir

        if "temp_dir" not in self.config["paths"]:
            # استخدام مجلد مؤقت في مجلد الإعدادات
            home_dir = os.path.expanduser("~")
            temp_dir = os.path.join(home_dir, ".media_translator", "temp")

            # إنشاء المجلد المؤقت إذا لم يكن موجودًا
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)

            self.config["paths"]["temp_dir"] = temp_dir

        if "output_dir" not in self.config["paths"]:
            # استخدام مجلد المستندات كمجلد افتراضي للإخراج
            output_dir = os.path.join(os.path.expanduser("~"), "Documents")
            self.config["paths"]["output_dir"] = output_dir

        # إعدادات الترجمة
        if "translation" not in self.config:
            self.config["translation"] = {}

        if "source_language" not in self.config["translation"]:
            self.config["translation"]["source_language"] = "auto"

        if "target_language" not in self.config["translation"]:
            self.config["translation"]["target_language"] = "ar"

        if "whisper_model" not in self.config["translation"]:
            self.config["translation"]["whisper_model"] = "medium"  # استخدام النموذج المتوسط للدقة العالية

        if "tts_model" not in self.config["translation"]:
            self.config["translation"]["tts_model"] = "tts_models/ar/fairseq/tts-transformer"

        if "voice_gender" not in self.config["translation"]:
            self.config["translation"]["voice_gender"] = "male"

        if "speech_rate" not in self.config["translation"]:
            self.config["translation"]["speech_rate"] = 1.0

        # إعدادات المعالجة
        if "processing" not in self.config:
            self.config["processing"] = {}

        if "quality" not in self.config["processing"]:
            self.config["processing"]["quality"] = "high"

        if "chunk_size" not in self.config["processing"]:
            self.config["processing"]["chunk_size"] = 30

        # إعدادات المشغل
        if "player" not in self.config:
            self.config["player"] = {}

        if "volume" not in self.config["player"]:
            self.config["player"]["volume"] = 70

        # إعدادات مسارات النماذج
        if "models" not in self.config:
            self.config["models"] = {}

        # مجلدات النماذج
        models_base_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "models")

        if "models_dir" not in self.config["models"]:
            self.config["models"]["models_dir"] = models_base_dir

        if "whisper_models_dir" not in self.config["models"]:
            self.config["models"]["whisper_models_dir"] = os.path.join(models_base_dir, "whisper")

        if "translation_models_dir" not in self.config["models"]:
            self.config["models"]["translation_models_dir"] = os.path.join(models_base_dir, "translation")

        if "tts_models_dir" not in self.config["models"]:
            self.config["models"]["tts_models_dir"] = os.path.join(models_base_dir, "tts")

        # مسارات النماذج المحددة
        if "whisper_medium_path" not in self.config["models"]:
            self.config["models"]["whisper_medium_path"] = ""

        if "translation_en_ar_path" not in self.config["models"]:
            self.config["models"]["translation_en_ar_path"] = ""

        if "translation_ar_en_path" not in self.config["models"]:
            self.config["models"]["translation_ar_en_path"] = ""

        if "tts_arabic_path" not in self.config["models"]:
            self.config["models"]["tts_arabic_path"] = ""

        if "tts_english_path" not in self.config["models"]:
            self.config["models"]["tts_english_path"] = ""

        # حفظ الإعدادات الافتراضية
        self.save()

    def load(self):
        """تحميل الإعدادات من الملف"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, "r", encoding="utf-8") as f:
                    self.config = json.load(f)
        except Exception as e:
            print(f"خطأ في تحميل ملف الإعدادات: {str(e)}")
            # استخدام الإعدادات الافتراضية
            self.config = defaultdict(dict)

    def save(self):
        """حفظ الإعدادات في الملف"""
        try:
            with open(self.config_file, "w", encoding="utf-8") as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"خطأ في حفظ ملف الإعدادات: {str(e)}")

    def get(self, section, key, default=None):
        """الحصول على قيمة إعداد"""
        try:
            return self.config[section][key]
        except KeyError:
            return default

    def set(self, section, key, value):
        """تعيين قيمة إعداد"""
        self.config[section][key] = value
        self.save()

    def add_recent_file(self, file_path):
        """إضافة ملف إلى قائمة الملفات الأخيرة"""
        recent_files = self.get("general", "recent_files", [])

        # إزالة المسار إذا كان موجودًا بالفعل
        if file_path in recent_files:
            recent_files.remove(file_path)

        # إضافة المسار في بداية القائمة
        recent_files.insert(0, file_path)

        # الاحتفاظ بأحدث 10 ملفات فقط
        recent_files = recent_files[:10]

        # تحديث القائمة
        self.set("general", "recent_files", recent_files)

    def clear_recent_files(self):
        """مسح قائمة الملفات الأخيرة"""
        self.set("general", "recent_files", [])
