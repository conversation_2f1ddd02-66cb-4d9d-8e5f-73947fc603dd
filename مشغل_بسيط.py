#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مشغل فيديو بسيط جداً - يشتغل 100%
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QPushButton, QFileDialog, QLabel
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtMultimediaWidgets import QVideoWidget
from PyQt5.QtCore import QUrl

class SimpleVideoPlayer(QWidget):
    def __init__(self):
        super().__init__()
        self.current_file = None
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("مشغل فيديو بسيط")
        self.setGeometry(100, 100, 800, 600)
        
        layout = QVBoxLayout()
        
        # زر اختيار الملف
        self.select_btn = QPushButton("اختر ملف فيديو")
        self.select_btn.clicked.connect(self.select_file)
        layout.addWidget(self.select_btn)
        
        # تسمية المعلومات
        self.info_label = QLabel("لم يتم اختيار ملف")
        layout.addWidget(self.info_label)
        
        # مشغل الفيديو
        self.video_widget = QVideoWidget()
        layout.addWidget(self.video_widget)
        
        # مشغل الوسائط
        self.player = QMediaPlayer()
        self.player.setVideoOutput(self.video_widget)
        
        # ربط الإشارات
        self.player.mediaStatusChanged.connect(self.on_status_changed)
        self.player.error.connect(self.on_error)
        
        # أزرار التحكم
        self.play_btn = QPushButton("تشغيل")
        self.play_btn.clicked.connect(self.toggle_play)
        self.play_btn.setEnabled(False)
        layout.addWidget(self.play_btn)
        
        self.setLayout(layout)
        
    def select_file(self):
        """اختيار ملف الفيديو"""
        print("🎬 فتح نافذة اختيار الملف...")
        
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختر ملف فيديو", "", 
            "Video Files (*.mp4 *.avi *.mkv *.mov *.wmv);;All Files (*)"
        )
        
        if file_path:
            print(f"✅ تم اختيار الملف: {file_path}")
            self.load_video(file_path)
        else:
            print("❌ لم يتم اختيار ملف")
    
    def load_video(self, file_path):
        """تحميل الفيديو"""
        print("=" * 60)
        print(f"🎬 تحميل الفيديو: {os.path.basename(file_path)}")
        print(f"📁 المسار: {file_path}")
        
        # التحقق من وجود الملف
        if not os.path.exists(file_path):
            print("❌ الملف غير موجود!")
            self.info_label.setText("❌ الملف غير موجود")
            return
        
        print("✅ الملف موجود")
        
        # حجم الملف
        try:
            file_size = os.path.getsize(file_path) / (1024 * 1024)
            print(f"📊 حجم الملف: {file_size:.1f} ميجابايت")
        except:
            print("⚠️ لا يمكن قراءة حجم الملف")
        
        self.current_file = file_path
        self.info_label.setText(f"تحميل: {os.path.basename(file_path)}")
        
        try:
            print("🔄 إنشاء URL...")
            file_url = QUrl.fromLocalFile(os.path.abspath(file_path))
            print(f"🔗 URL: {file_url.toString()}")
            
            if not file_url.isValid():
                print("❌ URL غير صالح!")
                self.info_label.setText("❌ URL غير صالح")
                return
            
            print("✅ URL صالح")
            
            print("🎵 إنشاء محتوى الوسائط...")
            media_content = QMediaContent(file_url)
            
            print("📺 تحميل في المشغل...")
            self.player.setMedia(media_content)
            
            print("✅ تم إرسال الملف للمشغل")
            print("⏳ انتظار استجابة المشغل...")
            
        except Exception as e:
            print(f"❌ خطأ في التحميل: {str(e)}")
            self.info_label.setText(f"❌ خطأ: {str(e)}")
        
        print("=" * 60)
    
    def toggle_play(self):
        """تبديل التشغيل"""
        if self.player.state() == QMediaPlayer.PlayingState:
            print("⏸️ إيقاف التشغيل")
            self.player.pause()
            self.play_btn.setText("تشغيل")
        else:
            print("▶️ بدء التشغيل")
            self.player.play()
            self.play_btn.setText("إيقاف")
    
    def on_status_changed(self, status):
        """معالجة تغيير الحالة"""
        status_names = {
            QMediaPlayer.NoMedia: "لا توجد وسائط",
            QMediaPlayer.LoadingMedia: "جاري التحميل",
            QMediaPlayer.LoadedMedia: "تم التحميل بنجاح",
            QMediaPlayer.StalledMedia: "متوقف",
            QMediaPlayer.BufferingMedia: "جاري التخزين المؤقت",
            QMediaPlayer.BufferedMedia: "جاهز للتشغيل",
            QMediaPlayer.EndOfMedia: "انتهى الملف",
            QMediaPlayer.InvalidMedia: "ملف غير صالح"
        }
        
        status_name = status_names.get(status, f"غير معروف ({status})")
        print(f"📊 حالة المشغل: {status_name}")
        
        if status == QMediaPlayer.LoadedMedia:
            print("🎉 نجح التحميل! يمكن التشغيل الآن")
            self.play_btn.setEnabled(True)
            self.info_label.setText(f"✅ جاهز: {os.path.basename(self.current_file)}")
        elif status == QMediaPlayer.InvalidMedia:
            print("❌ فشل التحميل - ملف غير صالح")
            self.play_btn.setEnabled(False)
            self.info_label.setText("❌ ملف غير صالح")
        elif status == QMediaPlayer.LoadingMedia:
            print("⏳ جاري التحميل...")
            self.info_label.setText("⏳ جاري التحميل...")
    
    def on_error(self, error):
        """معالجة الأخطاء"""
        error_names = {
            QMediaPlayer.NoError: "لا توجد أخطاء",
            QMediaPlayer.ResourceError: "خطأ في المورد",
            QMediaPlayer.FormatError: "خطأ في التنسيق",
            QMediaPlayer.NetworkError: "خطأ في الشبكة",
            QMediaPlayer.AccessDeniedError: "رفض الوصول"
        }
        
        error_name = error_names.get(error, f"خطأ غير معروف ({error})")
        print(f"🚨 خطأ في المشغل: {error_name}")
        
        if error != QMediaPlayer.NoError:
            self.info_label.setText(f"❌ خطأ: {error_name}")
            self.play_btn.setEnabled(False)

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    print("🎬 مشغل الفيديو البسيط")
    print("=" * 40)
    print("📋 التعليمات:")
    print("1. اضغط 'اختر ملف فيديو'")
    print("2. اختر ملف MP4")
    print("3. انتظر رسالة 'نجح التحميل'")
    print("4. اضغط 'تشغيل'")
    print("=" * 40)
    
    player = SimpleVideoPlayer()
    player.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
