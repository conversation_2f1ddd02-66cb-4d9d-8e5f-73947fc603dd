#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت تنزيل جميع النماذج المطلوبة مرة واحدة
"""

import os
import sys
import shutil
from pathlib import Path

def create_models_structure():
    """إنشاء هيكل مجلدات النماذج"""
    base_dir = Path(__file__).parent
    models_dir = base_dir / "models"
    
    # إنشاء المجلدات
    (models_dir / "whisper").mkdir(parents=True, exist_ok=True)
    (models_dir / "translation").mkdir(parents=True, exist_ok=True)
    (models_dir / "tts").mkdir(parents=True, exist_ok=True)
    
    return models_dir

def download_whisper_model(models_dir):
    """تنزيل نموذج Whisper"""
    print("=" * 50)
    print("📥 تنزيل نموذج Whisper Medium...")
    print("الحجم: ~1.5 جيجابايت")
    print("=" * 50)
    
    try:
        import whisper
        
        whisper_dir = models_dir / "whisper"
        model_path = whisper_dir / "medium.pt"
        
        if model_path.exists():
            print("✅ نموذج Whisper موجود بالفعل")
            return True
        
        print("🔄 جاري تنزيل نموذج Whisper...")
        
        # تنزيل النموذج
        model = whisper.load_model("medium")
        
        # نسخ النموذج إلى مجلد models
        cache_dir = Path.home() / ".cache" / "whisper"
        cache_model = cache_dir / "medium.pt"
        
        if cache_model.exists():
            shutil.copy2(cache_model, model_path)
            print("✅ تم تنزيل وحفظ نموذج Whisper بنجاح")
            return True
        else:
            print("❌ فشل في العثور على النموذج المنزل")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تنزيل نموذج Whisper: {str(e)}")
        return False

def download_translation_models(models_dir):
    """تنزيل نماذج الترجمة"""
    print("=" * 50)
    print("📥 تنزيل نماذج الترجمة...")
    print("الحجم: ~900 ميجابايت")
    print("=" * 50)
    
    try:
        from transformers import MarianMTModel, MarianTokenizer
        
        translation_dir = models_dir / "translation"
        
        # النماذج المطلوبة
        models_to_download = {
            "en-ar": "Helsinki-NLP/opus-mt-en-ar",
            "ar-en": "Helsinki-NLP/opus-mt-ar-en",
            "en-fr": "Helsinki-NLP/opus-mt-en-fr",
        }
        
        for model_key, model_name in models_to_download.items():
            model_path = translation_dir / model_key
            
            if model_path.exists() and any(model_path.iterdir()):
                print(f"✅ نموذج {model_key} موجود بالفعل")
                continue
            
            print(f"🔄 جاري تنزيل نموذج {model_key}...")
            
            try:
                # تنزيل النموذج والمحلل النصي
                tokenizer = MarianTokenizer.from_pretrained(model_name)
                model = MarianMTModel.from_pretrained(model_name)
                
                # حفظ النموذج محلياً
                tokenizer.save_pretrained(model_path)
                model.save_pretrained(model_path)
                
                print(f"✅ تم تنزيل نموذج {model_key} بنجاح")
                
            except Exception as e:
                print(f"❌ فشل في تنزيل نموذج {model_key}: {str(e)}")
        
        return True
        
    except ImportError:
        print("❌ مكتبة transformers غير مثبتة")
        print("قم بتشغيل: pip install transformers")
        return False
    except Exception as e:
        print(f"❌ خطأ في تنزيل نماذج الترجمة: {str(e)}")
        return False

def download_tts_models(models_dir):
    """تنزيل نماذج TTS"""
    print("=" * 50)
    print("📥 تنزيل نماذج TTS...")
    print("الحجم: ~1 جيجابايت")
    print("=" * 50)
    
    try:
        from TTS.api import TTS
        
        tts_dir = models_dir / "tts"
        
        # النماذج المطلوبة
        models_to_download = {
            "arabic": "tts_models/ar/fairseq/tts-transformer",
            "english": "tts_models/en/ljspeech/tacotron2-DDC",
        }
        
        for model_key, model_name in models_to_download.items():
            print(f"🔄 جاري تنزيل نموذج TTS {model_key}...")
            
            try:
                # تنزيل النموذج
                tts = TTS(model_name=model_name, progress_bar=True)
                print(f"✅ تم تنزيل نموذج TTS {model_key} بنجاح")
                
            except Exception as e:
                print(f"❌ فشل في تنزيل نموذج TTS {model_key}: {str(e)}")
        
        return True
        
    except ImportError:
        print("⚠️ مكتبة TTS غير مثبتة")
        print("سيتم استخدام gTTS كبديل")
        return True
    except Exception as e:
        print(f"❌ خطأ في تنزيل نماذج TTS: {str(e)}")
        return False

def check_disk_space():
    """التحقق من مساحة القرص المتاحة"""
    try:
        import shutil
        total, used, free = shutil.disk_usage(".")
        free_gb = free // (1024**3)
        
        print(f"💾 المساحة المتاحة: {free_gb} جيجابايت")
        
        if free_gb < 5:
            print("⚠️ تحذير: المساحة المتاحة قليلة. يُنصح بتوفير 5 جيجابايت على الأقل")
            return False
        
        return True
        
    except Exception:
        return True  # تجاهل الخطأ والمتابعة

def main():
    """الدالة الرئيسية"""
    print("🚀 مرحباً بك في أداة تنزيل النماذج")
    print("سيتم تنزيل جميع النماذج المطلوبة للبرنامج")
    print()
    
    # التحقق من مساحة القرص
    if not check_disk_space():
        response = input("هل تريد المتابعة؟ (y/n): ")
        if response.lower() != 'y':
            print("تم إلغاء العملية")
            return
    
    # إنشاء هيكل المجلدات
    print("📁 إنشاء مجلدات النماذج...")
    models_dir = create_models_structure()
    print("✅ تم إنشاء مجلدات النماذج")
    print()
    
    # تنزيل النماذج
    success_count = 0
    total_count = 3
    
    # 1. نموذج Whisper
    if download_whisper_model(models_dir):
        success_count += 1
    print()
    
    # 2. نماذج الترجمة
    if download_translation_models(models_dir):
        success_count += 1
    print()
    
    # 3. نماذج TTS
    if download_tts_models(models_dir):
        success_count += 1
    print()
    
    # النتيجة النهائية
    print("=" * 50)
    print("📊 ملخص التنزيل:")
    print(f"✅ تم بنجاح: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 تم تنزيل جميع النماذج بنجاح!")
        print("يمكنك الآن استخدام البرنامج بدون إنترنت")
    else:
        print("⚠️ بعض النماذج لم يتم تنزيلها")
        print("البرنامج سيعمل مع النماذج المتاحة")
    
    print("=" * 50)
    
    # عرض أحجام الملفات
    print("\n📊 أحجام النماذج المنزلة:")
    for model_type in ["whisper", "translation", "tts"]:
        model_path = models_dir / model_type
        if model_path.exists():
            size = sum(f.stat().st_size for f in model_path.rglob('*') if f.is_file())
            size_mb = size / (1024 * 1024)
            print(f"  {model_type}: {size_mb:.1f} ميجابايت")
    
    print("\nاضغط Enter للخروج...")
    input()

if __name__ == "__main__":
    main()
