#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة حول البرنامج
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTab<PERSON>idget, Q<PERSON><PERSON><PERSON>, QTextBrowser)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPixmap, QFont

class AboutDialog(QDialog):
    """نافذة حول البرنامج"""
    
    def __init__(self, parent=None):
        """تهيئة نافذة حول البرنامج"""
        super().__init__(parent)
        
        self._init_ui()
    
    def _init_ui(self):
        """تهيئة واجهة المستخدم"""
        # تعيين عنوان النافذة
        self.setWindowTitle("حول البرنامج")
        self.setMinimumSize(500, 400)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        
        # إنشاء علامات التبويب
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # إنشاء علامة تبويب حول البرنامج
        self.about_tab = QWidget()
        self.tab_widget.addTab(self.about_tab, "حول البرنامج")
        
        # إنشاء علامة تبويب الترخيص
        self.license_tab = QWidget()
        self.tab_widget.addTab(self.license_tab, "الترخيص")
        
        # إنشاء علامة تبويب المكتبات
        self.libraries_tab = QWidget()
        self.tab_widget.addTab(self.libraries_tab, "المكتبات")
        
        # إنشاء محتوى علامة تبويب حول البرنامج
        self._init_about_tab()
        
        # إنشاء محتوى علامة تبويب الترخيص
        self._init_license_tab()
        
        # إنشاء محتوى علامة تبويب المكتبات
        self._init_libraries_tab()
        
        # إنشاء زر الإغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(self.accept)
        
        # إضافة زر الإغلاق إلى التخطيط الرئيسي
        button_layout = QHBoxLayout()
        button_layout.addStretch(1)
        button_layout.addWidget(close_button)
        layout.addLayout(button_layout)
    
    def _init_about_tab(self):
        """تهيئة علامة تبويب حول البرنامج"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self.about_tab)
        
        # إنشاء عنوان البرنامج
        title_label = QLabel("مشغل الوسائط مع الدبلجة الآلية")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        
        # إنشاء نسخة البرنامج
        version_label = QLabel("الإصدار 1.0.0")
        version_label.setAlignment(Qt.AlignCenter)
        
        # إنشاء وصف البرنامج
        description_label = QLabel(
            "برنامج لتشغيل ملفات الفيديو مع إمكانية استخراج النص وترجمته "
            "وتحويله إلى كلام ودبلجة الفيديو بشكل آلي."
        )
        description_label.setWordWrap(True)
        description_label.setAlignment(Qt.AlignCenter)
        
        # إنشاء معلومات المطور
        developer_label = QLabel("تطوير: فريق المترجم الآلي")
        developer_label.setAlignment(Qt.AlignCenter)
        
        # إنشاء حقوق النشر
        copyright_label = QLabel("© 2023 جميع الحقوق محفوظة")
        copyright_label.setAlignment(Qt.AlignCenter)
        
        # إضافة العناصر إلى التخطيط الرئيسي
        layout.addStretch(1)
        layout.addWidget(title_label)
        layout.addWidget(version_label)
        layout.addSpacing(20)
        layout.addWidget(description_label)
        layout.addSpacing(20)
        layout.addWidget(developer_label)
        layout.addWidget(copyright_label)
        layout.addStretch(1)
    
    def _init_license_tab(self):
        """تهيئة علامة تبويب الترخيص"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self.license_tab)
        
        # إنشاء عنوان الترخيص
        title_label = QLabel("ترخيص MIT")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        
        # إنشاء نص الترخيص
        license_browser = QTextBrowser()
        license_browser.setOpenExternalLinks(True)
        license_browser.setHtml("""
            <div dir="rtl" style="text-align: right;">
                <p>حقوق النشر © 2023 فريق المترجم الآلي</p>
                
                <p>يُمنح الإذن، مجانًا، لأي شخص يحصل على نسخة من هذا البرنامج وملفات التوثيق المرتبطة به (البرنامج)، للتعامل مع البرنامج دون قيود، بما في ذلك على سبيل المثال لا الحصر حقوق استخدام ونسخ وتعديل ودمج ونشر وتوزيع وترخيص من الباطن و/أو بيع نسخ من البرنامج، وللسماح للأشخاص الذين تم توفير البرنامج لهم بالقيام بذلك، وفقًا للشروط التالية:</p>
                
                <p>يجب أن يتضمن إشعار حقوق النشر أعلاه وإشعار الإذن هذا في جميع نسخ أو أجزاء كبيرة من البرنامج.</p>
                
                <p>يتم توفير البرنامج "كما هو"، دون أي ضمان من أي نوع، صريح أو ضمني، بما في ذلك على سبيل المثال لا الحصر ضمانات القابلية للتسويق والملاءمة لغرض معين وعدم الانتهاك. لا يتحمل المؤلفون أو حاملو حقوق النشر بأي حال من الأحوال المسؤولية عن أي مطالبة أو أضرار أو مسؤولية أخرى، سواء في إجراء العقد أو الضرر أو غير ذلك، الناشئة عن أو خارج أو في اتصال مع البرنامج أو استخدام أو تعاملات أخرى في البرنامج.</p>
            </div>
        """)
        
        # إضافة العناصر إلى التخطيط الرئيسي
        layout.addWidget(title_label)
        layout.addWidget(license_browser)
    
    def _init_libraries_tab(self):
        """تهيئة علامة تبويب المكتبات"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self.libraries_tab)
        
        # إنشاء عنوان المكتبات
        title_label = QLabel("المكتبات المستخدمة")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        
        # إنشاء قائمة المكتبات
        libraries_browser = QTextBrowser()
        libraries_browser.setOpenExternalLinks(True)
        libraries_browser.setHtml("""
            <div dir="rtl" style="text-align: right;">
                <p>يستخدم هذا البرنامج المكتبات التالية:</p>
                
                <ul>
                    <li><a href="https://www.python.org/">Python</a> - لغة البرمجة الأساسية</li>
                    <li><a href="https://www.riverbankcomputing.com/software/pyqt/">PyQt5</a> - إطار واجهة المستخدم الرسومية</li>
                    <li><a href="https://github.com/openai/whisper">OpenAI Whisper</a> - نظام التعرف على الكلام</li>
                    <li><a href="https://github.com/coqui-ai/TTS">Coqui TTS</a> - نظام تحويل النص إلى كلام</li>
                    <li><a href="https://github.com/huggingface/transformers">Hugging Face Transformers</a> - نماذج الترجمة</li>
                    <li><a href="https://github.com/Zulko/moviepy">MoviePy</a> - معالجة الفيديو</li>
                    <li><a href="https://github.com/jiaaro/pydub">PyDub</a> - معالجة الصوت</li>
                    <li><a href="https://pytorch.org/">PyTorch</a> - إطار التعلم الآلي</li>
                </ul>
                
                <p>نشكر جميع المطورين والمساهمين في هذه المكتبات المفتوحة المصدر.</p>
            </div>
        """)
        
        # إضافة العناصر إلى التخطيط الرئيسي
        layout.addWidget(title_label)
        layout.addWidget(libraries_browser)
