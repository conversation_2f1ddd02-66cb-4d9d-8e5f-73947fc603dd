#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مشغل الوسائط مع الدبلجة الآلية
تطبيق لتشغيل ملفات الفيديو مع إمكانية استخراج النص وترجمته وتحويله إلى كلام ودبلجة الفيديو بشكل آلي.
"""

import sys
import os
import argparse
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

# إضافة المجلد الحالي إلى مسار Python
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# استيراد المكونات الأساسية
from core.config import Config
from core.simple_media_processor import SimpleMediaProcessor
from core.translation_engine import TranslationEngine
from core.tts_engine import TTSEngine
from ui.main_window import MainWindow

def parse_arguments():
    """تحليل وسائط سطر الأوامر"""
    parser = argparse.ArgumentParser(description="مشغل الوسائط مع الدبلجة الآلية")

    parser.add_argument(
        "--config",
        help="مسار ملف الإعدادات",
        default=None
    )

    parser.add_argument(
        "--file",
        help="مسار ملف الفيديو للفتح",
        default=None
    )

    return parser.parse_args()

def check_dependencies():
    """التحقق من وجود المكتبات والنماذج المطلوبة"""
    try:
        import whisper
        import torch
        import moviepy
        from gtts import gTTS
        from transformers import MarianMTModel, MarianTokenizer
    except ImportError as e:
        print(f"خطأ: بعض المكتبات المطلوبة غير مثبتة: {e}")
        print("يرجى تثبيت المكتبات المطلوبة باستخدام: pip install -r requirements.txt")
        return False

    return True

def main():
    """الدالة الرئيسية"""
    # تحليل وسائط سطر الأوامر
    args = parse_arguments()

    # التحقق من المكتبات المطلوبة
    if not check_dependencies():
        sys.exit(1)

    # تهيئة التطبيق
    app = QApplication(sys.argv)

    # تعيين اتجاه النص من اليمين إلى اليسار
    app.setLayoutDirection(Qt.RightToLeft)

    # تهيئة الإعدادات
    config = Config(args.config)

    # تهيئة محركات المعالجة
    translation_engine = TranslationEngine(config)
    tts_engine = TTSEngine(config)

    # إنشاء النافذة الرئيسية
    main_window = MainWindow(config)
    main_window.show()

    # فتح ملف الفيديو إذا تم تحديده
    if args.file and os.path.exists(args.file):
        main_window.open_file(args.file)

    # تشغيل التطبيق
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
