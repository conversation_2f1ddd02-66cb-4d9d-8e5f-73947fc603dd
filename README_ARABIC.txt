مشغل الوسائط مع الدبلجة الآلية المحلية
=====================================

هذا البرنامج يسمح لك بتشغيل ملفات الفيديو مع إمكانية استخراج النص وترجمته وتحويله إلى كلام ودبلجة الفيديو بشكل آلي.
البرنامج يعمل بالكامل بدون إنترنت بعد تنزيل النماذج المطلوبة.

الميزات الجديدة
--------------
✅ **دبلجة شاملة**: زر واحد لتنفيذ جميع المراحل (استخراج النص → ترجمة → توليد الصوت → دبلجة)
✅ **نماذج محلية**: جميع النماذج تعمل بدون إنترنت بعد التنزيل الأولي
✅ **ترجمة محلية**: استخدام نماذج Helsinki-NLP للترجمة بدون إنترنت
✅ **TTS محلي**: دعم Coqui TTS للحصول على جودة صوت أفضل
✅ **محرك دبلجة متقدم**: دمج الصوت مع خيارات تحكم متقدمة

متطلبات النظام
--------------
- Python 3.8 أو أحدث
- ffmpeg (مطلوب لمعالجة الفيديو والصوت)
- مساحة تخزين: 3-5 جيجابايت للنماذج

طريقة التشغيل السريع
------------------
1. قم بتثبيت Python من https://www.python.org/downloads/
2. قم بتثبيت ffmpeg من https://ffmpeg.org/download.html وإضافته إلى متغير PATH
3. انقر نقرًا مزدوجًا على ملف `تشغيل.bat` لتشغيل البرنامج
4. إذا ظهرت رسالة خطأ، شغل `install_requirements.bat` أولاً
5. افتح ملف فيديو واضغط على "دبلجة شاملة" - سيتم كل شيء تلقائياً!

إعداد النماذج (مرة واحدة فقط)
-----------------------------
البرنامج يستخدم عدة نماذج ذكاء اصطناعي بحجم إجمالي ~3.5 جيجابايت:

### طريقة الإعداد السريع (مُوصى بها):
1. شغل ملف `download_models.bat`
2. انتظر حتى اكتمال التنزيل (~10-30 دقيقة حسب سرعة الإنترنت)
3. بعد ذلك البرنامج سيعمل بدون إنترنت!

### تفاصيل النماذج:

#### 1. نموذج Whisper (استخراج النص):
- **الحجم**: ~1.5 جيجابايت
- **الوظيفة**: تحويل الكلام إلى نص بدقة عالية
- **يُحفظ في**: `models/whisper/`

#### 2. نماذج الترجمة (Helsinki-NLP):
- **الحجم**: ~900 ميجابايت (3 نماذج)
- **الوظيفة**: ترجمة بدون إنترنت (عربي ↔ إنجليزي، إنجليزي ↔ فرنسي)
- **يُحفظ في**: `models/translation/`

#### 3. نماذج TTS (تحويل النص إلى كلام):
- **الحجم**: ~1 جيجابايت
- **الوظيفة**: توليد صوت عالي الجودة
- **يُحفظ في**: `models/tts/`
- **بديل**: gTTS (يتطلب إنترنت)

طريقة الاستخدام
--------------

### الطريقة السريعة (دبلجة شاملة):
1. شغل البرنامج بالنقر على `run.bat`
2. انقر على "فتح ملف" لاختيار فيديو
3. انقر على "دبلجة شاملة" - سيتم كل شيء تلقائياً!

### الطريقة التفصيلية (خطوة بخطوة):
1. شغل البرنامج بالنقر على `run.bat`
2. انقر على "فتح ملف" لاختيار فيديو
3. استخدم أزرار التحكم في الفيديو:
   - مفتاح المسافة: تشغيل/إيقاف
   - الأسهم: تقديم/إرجاع، رفع/خفض الصوت
   - M: كتم الصوت
   - F: ملء الشاشة
4. انقر على "استخراج النص" لاستخراج النص من الفيديو
5. انقر على "ترجمة" لترجمة النص (محلياً)
6. انقر على "توليد الصوت" لتحويل النص إلى كلام
7. انقر على "دبلجة" لدمج الصوت مع الفيديو

هيكل المشروع
------------
```
المشروع/
├── run.bat                    (ملف التشغيل الرئيسي)
├── setup_whisper.bat         (إعداد النماذج)
├── check_whisper_model.py    (البحث عن النماذج)
├── main.py                   (الملف الرئيسي)
├── models/                   (مجلد النماذج المحلية)
│   ├── whisper/             (نماذج Whisper)
│   ├── translation/         (نماذج الترجمة)
│   └── tts/                 (نماذج TTS)
├── core/                     (محركات المعالجة)
│   ├── simple_media_processor.py
│   ├── translation_engine.py
│   ├── tts_engine.py
│   └── dubbing_engine.py
└── ui/                       (واجهة المستخدم)
    ├── main_window.py
    ├── simple_video_player.py
    └── translation_panel.py
```

الملفات المهمة
--------------
### ملفات التشغيل:
- `تشغيل.bat` - الملف الوحيد لتشغيل البرنامج
- `install_requirements.bat` - تثبيت المكتبات المطلوبة

### ملفات التشخيص:
- `check_models.py` - فحص شامل لجميع النماذج والمتطلبات
- `test_video.py` - اختبار تحميل الفيديو وتشخيص المشاكل

### ملفات إعداد النماذج:
- `download_models.bat` - تنزيل جميع النماذج مرة واحدة
- `setup_whisper.bat` - إعداد النماذج الموجودة
- `download_all_models.py` - أداة تنزيل النماذج المتقدمة

### ملفات المعلومات:
- `README_ARABIC.txt` - دليل الاستخدام الشامل
- `MODELS_INFO.txt` - معلومات مفصلة عن النماذج

### ملفات البرنامج:
- `main.py` - الملف الرئيسي للبرنامج
- `check_whisper_model.py` - للبحث عن النماذج في الجهاز

المكتبات المطلوبة
-----------------
- PyQt5 (واجهة المستخدم)
- openai-whisper (استخراج النص)
- transformers (الترجمة المحلية)
- torch (دعم النماذج)
- TTS (Coqui TTS - اختياري)
- gtts (بديل TTS)
- pydub (معالجة الصوت)
- numpy (العمليات الرياضية)

ملاحظات مهمة
------------
✅ البرنامج يعمل بالكامل بدون إنترنت بعد تنزيل النماذج
✅ النماذج تُنزل مرة واحدة فقط وتُحفظ للاستخدام المستقبلي
✅ يدعم ملفات الفيديو: MP4, AVI, MKV, MOV, WMV
✅ جودة ترجمة وصوت عالية باستخدام النماذج المحلية
✅ واجهة عربية بالكامل مع دعم اختصارات لوحة المفاتيح

استكشاف الأخطاء
---------------

### مشاكل التشغيل:

**المشكلة**: البرنامج لا يشتغل
**الحل**:
1. استخدم `تشغيل.bat`
2. إذا ظهرت رسالة خطأ، شغل `install_requirements.bat` أولاً
3. تأكد من تثبيت Python 3.8 أو أحدث

**المشكلة**: فشل في تحميل ملف الفيديو
**الحل**:
1. شغل `python test_video.py` لتشخيص المشكلة
2. جرب ملف MP4 بدلاً من التنسيقات الأخرى
3. تأكد من أن الملف غير تالف
4. تجنب المسارات التي تحتوي على أحرف خاصة أو عربية

**المشكلة**: رسالة خطأ "Python غير موجود"
**الحل**:
1. ثبت Python من https://www.python.org/downloads/
2. تأكد من إضافة Python إلى PATH أثناء التثبيت

**المشكلة**: رسالة خطأ "مكتبة غير موجودة"
**الحل**:
1. شغل `install_requirements.bat`
2. انتظر حتى اكتمال تثبيت جميع المكتبات

### مشاكل الوظائف:

**المشكلة**: لا يعمل استخراج النص
**الحل**: تأكد من وجود نموذج Whisper في `models/whisper/`

**المشكلة**: لا تعمل الترجمة
**الحل**: سيتم تنزيل النماذج تلقائياً عند أول استخدام

**المشكلة**: لا يعمل توليد الصوت
**الحل**: سيتم استخدام gTTS كبديل (يتطلب إنترنت)

**المشكلة**: لا تعمل الدبلجة
**الحل**: تأكد من تثبيت ffmpeg وإضافته إلى PATH

### نصائح عامة:
- استخدم ملفات MP4 للحصول على أفضل توافق
- تجنب المسارات التي تحتوي على أحرف عربية أو خاصة
- تأكد من وجود مساحة كافية (5 جيجابايت)
- تأكد من اتصال الإنترنت عند أول استخدام
- استخدم `test_video.py` لتشخيص مشاكل الفيديو
