#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Comprehensive video compatibility fix and testing system
"""

import os
import sys
import subprocess
from pathlib import Path

def check_video_codecs():
    """Check available video codecs in the system"""
    print("🎬 Checking Video Codec Support")
    print("=" * 50)
    
    try:
        # Check ffmpeg codecs
        result = subprocess.run(["ffmpeg", "-codecs"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            codecs_output = result.stdout
            
            # Check for common video codecs
            important_codecs = {
                "h264": "H.264/AVC",
                "hevc": "H.265/HEVC", 
                "mpeg4": "MPEG-4",
                "xvid": "Xvid",
                "vp8": "VP8",
                "vp9": "VP9"
            }
            
            print("📋 Available Video Codecs:")
            for codec, name in important_codecs.items():
                if codec in codecs_output.lower():
                    print(f"   ✅ {name} ({codec})")
                else:
                    print(f"   ❌ {name} ({codec})")
            
            return True
        else:
            print("❌ Could not check ffmpeg codecs")
            return False
            
    except Exception as e:
        print(f"❌ Error checking codecs: {str(e)}")
        return False

def test_qt_multimedia():
    """Test Qt multimedia capabilities"""
    print("\n🎵 Testing Qt Multimedia Support")
    print("=" * 50)
    
    try:
        from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
        from PyQt5.QtCore import QUrl
        from PyQt5.QtWidgets import QApplication
        
        # Create minimal Qt application
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Test media player creation
        player = QMediaPlayer()
        print("✅ QMediaPlayer created successfully")
        
        # Check supported formats
        print("\n📋 Qt Multimedia Information:")
        print(f"   Player state: {player.state()}")
        print(f"   Media status: {player.mediaStatus()}")
        print(f"   Error status: {player.error()}")
        
        # Test with a simple URL
        test_url = QUrl("file:///test.mp4")
        print(f"   Test URL creation: {'✅ Success' if test_url.isValid() else '❌ Failed'}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Qt Multimedia not available: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Qt Multimedia test failed: {str(e)}")
        return False

def create_test_video():
    """Create a simple test video file"""
    print("\n🎬 Creating Test Video File")
    print("=" * 50)
    
    test_video_path = Path("test_video.mp4")
    
    try:
        # Create a simple test video using ffmpeg
        cmd = [
            "ffmpeg", "-y",  # Overwrite existing file
            "-f", "lavfi",   # Use libavfilter
            "-i", "testsrc=duration=5:size=320x240:rate=1",  # Test source
            "-c:v", "libx264",  # H.264 codec
            "-pix_fmt", "yuv420p",  # Pixel format
            "-t", "5",  # Duration 5 seconds
            str(test_video_path)
        ]
        
        print("🔄 Generating test video...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and test_video_path.exists():
            file_size = test_video_path.stat().st_size
            print(f"✅ Test video created: {test_video_path} ({file_size} bytes)")
            return str(test_video_path)
        else:
            print(f"❌ Failed to create test video: {result.stderr}")
            return None
            
    except subprocess.TimeoutExpired:
        print("❌ Test video creation timed out")
        return None
    except Exception as e:
        print(f"❌ Error creating test video: {str(e)}")
        return None

def test_video_loading(video_path):
    """Test video loading with Qt"""
    print(f"\n🧪 Testing Video Loading: {video_path}")
    print("=" * 50)
    
    try:
        from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
        from PyQt5.QtCore import QUrl, QTimer
        from PyQt5.QtWidgets import QApplication
        import time
        
        # Ensure Qt application exists
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create media player
        player = QMediaPlayer()
        
        # Track status changes
        status_changes = []
        error_messages = []
        
        def on_media_status_changed(status):
            status_names = {
                QMediaPlayer.NoMedia: "NoMedia",
                QMediaPlayer.LoadingMedia: "LoadingMedia", 
                QMediaPlayer.LoadedMedia: "LoadedMedia",
                QMediaPlayer.StalledMedia: "StalledMedia",
                QMediaPlayer.BufferingMedia: "BufferingMedia",
                QMediaPlayer.BufferedMedia: "BufferedMedia",
                QMediaPlayer.EndOfMedia: "EndOfMedia",
                QMediaPlayer.InvalidMedia: "InvalidMedia"
            }
            status_name = status_names.get(status, f"Unknown({status})")
            status_changes.append(status_name)
            print(f"   📊 Media Status: {status_name}")
        
        def on_error(error):
            error_names = {
                QMediaPlayer.NoError: "NoError",
                QMediaPlayer.ResourceError: "ResourceError",
                QMediaPlayer.FormatError: "FormatError", 
                QMediaPlayer.NetworkError: "NetworkError",
                QMediaPlayer.AccessDeniedError: "AccessDeniedError"
            }
            error_name = error_names.get(error, f"Unknown({error})")
            error_messages.append(error_name)
            print(f"   ❌ Error: {error_name}")
        
        # Connect signals
        player.mediaStatusChanged.connect(on_media_status_changed)
        player.error.connect(on_error)
        
        # Test file path
        abs_path = os.path.abspath(video_path)
        print(f"   📁 Absolute path: {abs_path}")
        print(f"   📋 File exists: {os.path.exists(abs_path)}")
        
        if os.path.exists(abs_path):
            file_size = os.path.getsize(abs_path)
            print(f"   📊 File size: {file_size} bytes")
        
        # Create URL and media content
        file_url = QUrl.fromLocalFile(abs_path)
        print(f"   🔗 URL: {file_url.toString()}")
        print(f"   ✅ URL valid: {file_url.isValid()}")
        
        media_content = QMediaContent(file_url)
        
        # Load media
        print("   🔄 Loading media...")
        player.setMedia(media_content)
        
        # Wait for loading
        for i in range(50):  # Wait up to 5 seconds
            app.processEvents()
            time.sleep(0.1)
            
            if status_changes and status_changes[-1] in ["LoadedMedia", "InvalidMedia"]:
                break
        
        # Results
        print(f"\n📊 Test Results:")
        print(f"   Status changes: {' → '.join(status_changes)}")
        print(f"   Errors: {error_messages if error_messages else 'None'}")
        print(f"   Final status: {player.mediaStatus()}")
        print(f"   Duration: {player.duration()} ms")
        
        success = (player.mediaStatus() == QMediaPlayer.LoadedMedia and 
                  not error_messages)
        
        if success:
            print("   🎉 Video loading test: ✅ SUCCESS")
        else:
            print("   ❌ Video loading test: FAILED")
        
        return success
        
    except Exception as e:
        print(f"❌ Video loading test error: {str(e)}")
        return False

def fix_video_player():
    """Apply fixes to the video player"""
    print("\n🔧 Applying Video Player Fixes")
    print("=" * 50)
    
    # Check if we need to install additional codecs
    print("💡 Recommended fixes:")
    print("   1. Install K-Lite Codec Pack for Windows")
    print("   2. Use MP4 files with H.264 codec")
    print("   3. Avoid special characters in file paths")
    print("   4. Ensure files are not corrupted")
    
    return True

def main():
    """Main diagnostic and fix function"""
    print("🔍 Video Compatibility Diagnostic & Fix System")
    print("=" * 60)
    
    # Step 1: Check codecs
    codecs_ok = check_video_codecs()
    
    # Step 2: Test Qt multimedia
    qt_ok = test_qt_multimedia()
    
    # Step 3: Create test video
    test_video = create_test_video()
    
    # Step 4: Test video loading
    loading_ok = False
    if test_video:
        loading_ok = test_video_loading(test_video)
        
        # Clean up test video
        try:
            os.remove(test_video)
            print(f"🗑️ Cleaned up test video: {test_video}")
        except:
            pass
    
    # Step 5: Apply fixes
    fix_video_player()
    
    # Final report
    print("\n" + "=" * 60)
    print("📊 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    print(f"🎬 FFmpeg Codecs: {'✅ Available' if codecs_ok else '❌ Issues'}")
    print(f"🎵 Qt Multimedia: {'✅ Working' if qt_ok else '❌ Issues'}")
    print(f"📹 Test Video Creation: {'✅ Success' if test_video else '❌ Failed'}")
    print(f"🧪 Video Loading Test: {'✅ Success' if loading_ok else '❌ Failed'}")
    
    overall_status = all([codecs_ok, qt_ok, loading_ok])
    print(f"\n🎯 Overall Status: {'🎉 READY' if overall_status else '⚠️ NEEDS ATTENTION'}")
    
    if not overall_status:
        print("\n💡 Recommendations:")
        if not codecs_ok:
            print("   • Install additional video codecs")
        if not qt_ok:
            print("   • Reinstall PyQt5 multimedia components")
        if not loading_ok:
            print("   • Test with different video files")
            print("   • Check file permissions and paths")
    
    return overall_status

if __name__ == "__main__":
    success = main()
    
    print(f"\nPress Enter to exit...")
    input()
