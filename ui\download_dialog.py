#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة تنزيل النماذج
"""

import threading
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QProgressBar)
from PyQt5.QtCore import Qt, pyqtSignal

class DownloadDialog(QDialog):
    """نافذة تنزيل النماذج"""
    
    # الإشارات
    download_finished = pyqtSignal(bool)  # نجاح التنزيل
    progress_updated = pyqtSignal(int, str)  # تحديث التقدم (النسبة، الرسالة)
    
    def __init__(self, parent=None, title="تنزيل النموذج", model_name=""):
        """تهيئة نافذة التنزيل"""
        super().__init__(parent)
        
        self.model_name = model_name
        self.download_thread = None
        self.is_downloading = False
        
        self._init_ui(title)
        self._connect_signals()
    
    def _init_ui(self, title):
        """تهيئة واجهة المستخدم"""
        # تعيين عنوان النافذة
        self.setWindowTitle(title)
        self.setMinimumSize(400, 150)
        self.setModal(True)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        
        # إنشاء عنوان التنزيل
        self.title_label = QLabel(f"جاري تنزيل النموذج: {self.model_name}")
        self.title_label.setAlignment(Qt.AlignCenter)
        
        # إنشاء وصف التنزيل
        self.description_label = QLabel(
            "يرجى الانتظار أثناء تنزيل النموذج. قد يستغرق ذلك بعض الوقت حسب حجم النموذج وسرعة الإنترنت."
        )
        self.description_label.setWordWrap(True)
        self.description_label.setAlignment(Qt.AlignCenter)
        
        # إنشاء شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        
        # إنشاء عرض حالة التنزيل
        self.status_label = QLabel("جاري التحضير للتنزيل...")
        self.status_label.setAlignment(Qt.AlignCenter)
        
        # إنشاء أزرار التحكم
        button_layout = QHBoxLayout()
        
        # زر الإلغاء
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        # إضافة العناصر إلى التخطيط الرئيسي
        layout.addWidget(self.title_label)
        layout.addWidget(self.description_label)
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.status_label)
        layout.addLayout(button_layout)
    
    def _connect_signals(self):
        """ربط الإشارات"""
        # ربط إشارات التقدم
        self.progress_updated.connect(self._on_progress_updated)
        self.download_finished.connect(self._on_download_finished)
    
    def _on_progress_updated(self, progress, message):
        """معالجة حدث تحديث التقدم"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(message)
    
    def _on_download_finished(self, success):
        """معالجة حدث انتهاء التنزيل"""
        self.is_downloading = False
        
        if success:
            self.status_label.setText("تم تنزيل النموذج بنجاح")
            self.progress_bar.setValue(100)
            self.cancel_button.setText("إغلاق")
        else:
            self.status_label.setText("فشل في تنزيل النموذج")
            self.progress_bar.setValue(0)
            self.cancel_button.setText("إغلاق")
    
    def start_download(self, download_function, model_name):
        """بدء تنزيل النموذج"""
        if self.is_downloading:
            return
        
        self.is_downloading = True
        
        # إنشاء خيط التنزيل
        def download_thread():
            try:
                # تنزيل النموذج
                success = download_function(
                    model_name,
                    progress_callback=self._update_progress
                )
                
                # إرسال إشارة انتهاء التنزيل
                self.download_finished.emit(success)
            except Exception as e:
                print(f"خطأ في تنزيل النموذج: {str(e)}")
                self.download_finished.emit(False)
        
        # بدء الخيط
        self.download_thread = threading.Thread(target=download_thread)
        self.download_thread.daemon = True
        self.download_thread.start()
    
    def _update_progress(self, progress, message):
        """تحديث التقدم"""
        # إرسال إشارة تحديث التقدم
        self.progress_updated.emit(progress, message)
    
    def reject(self):
        """إلغاء التنزيل وإغلاق النافذة"""
        if self.is_downloading:
            # لا يمكن إلغاء التنزيل حاليًا، لكن يمكن إغلاق النافذة
            # ويستمر التنزيل في الخلفية
            pass
        
        # إغلاق النافذة
        super().reject()
