#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار بسيط لتشغيل الفيديو
"""

import os
import sys
import tempfile
import shutil
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QPushButton, QLabel, QFileDialog
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtMultimediaWidgets import QVideoWidget
from PyQt5.QtCore import QUrl, pyqtSignal

class SimpleVideoTest(QWidget):
    def __init__(self):
        super().__init__()
        self.temp_file = None
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("اختبار تشغيل الفيديو")
        self.setGeometry(100, 100, 800, 600)
        
        layout = QVBoxLayout()
        
        # زر اختيار الملف
        self.select_btn = QPushButton("اختر ملف فيديو")
        self.select_btn.clicked.connect(self.select_file)
        layout.addWidget(self.select_btn)
        
        # تسمية لعرض المعلومات
        self.info_label = QLabel("لم يتم اختيار ملف")
        layout.addWidget(self.info_label)
        
        # مشغل الفيديو
        self.video_widget = QVideoWidget()
        layout.addWidget(self.video_widget)
        
        # مشغل الوسائط
        self.player = QMediaPlayer()
        self.player.setVideoOutput(self.video_widget)
        
        # ربط الإشارات
        self.player.mediaStatusChanged.connect(self.on_media_status_changed)
        self.player.error.connect(self.on_error)
        
        # أزرار التحكم
        control_layout = QVBoxLayout()
        
        self.play_btn = QPushButton("تشغيل")
        self.play_btn.clicked.connect(self.toggle_play)
        self.play_btn.setEnabled(False)
        control_layout.addWidget(self.play_btn)
        
        self.test_copy_btn = QPushButton("اختبار مع نسخة مؤقتة")
        self.test_copy_btn.clicked.connect(self.test_with_temp_copy)
        self.test_copy_btn.setEnabled(False)
        control_layout.addWidget(self.test_copy_btn)
        
        layout.addLayout(control_layout)
        
        self.setLayout(layout)
        
    def select_file(self):
        """اختيار ملف الفيديو"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختر ملف فيديو", "", 
            "Video Files (*.mp4 *.avi *.mkv *.mov *.wmv);;All Files (*)"
        )
        
        if file_path:
            self.current_file = file_path
            self.info_label.setText(f"الملف: {os.path.basename(file_path)}")
            self.load_video(file_path)
            self.test_copy_btn.setEnabled(True)
    
    def create_temp_copy(self, file_path):
        """إنشاء نسخة مؤقتة بمسار إنجليزي"""
        try:
            # تنظيف النسخة المؤقتة السابقة
            if self.temp_file and os.path.exists(self.temp_file):
                try:
                    os.remove(self.temp_file)
                except:
                    pass
            
            # إنشاء مجلد مؤقت
            temp_dir = tempfile.mkdtemp(prefix="video_test_")
            
            # الحصول على امتداد الملف
            file_ext = os.path.splitext(file_path)[1]
            
            # إنشاء اسم ملف مؤقت بسيط
            temp_filename = f"test_video{file_ext}"
            temp_path = os.path.join(temp_dir, temp_filename)
            
            print(f"إنشاء نسخة مؤقتة: {temp_path}")
            
            # نسخ الملف
            shutil.copy2(file_path, temp_path)
            
            if os.path.exists(temp_path):
                self.temp_file = temp_path
                return temp_path
            else:
                return None
                
        except Exception as e:
            print(f"خطأ في إنشاء النسخة المؤقتة: {str(e)}")
            return None
    
    def load_video(self, file_path):
        """تحميل الفيديو"""
        try:
            print(f"تحميل الفيديو: {file_path}")
            
            # التحقق من المسار
            has_arabic = any(ord(char) > 127 for char in file_path)
            if has_arabic:
                print("⚠️ المسار يحتوي على أحرف عربية")
            
            # تحويل إلى مسار مطلق
            abs_path = os.path.abspath(file_path)
            print(f"المسار المطلق: {abs_path}")
            
            # إنشاء URL
            file_url = QUrl.fromLocalFile(abs_path)
            print(f"URL: {file_url.toString()}")
            
            if not file_url.isValid():
                print("❌ URL غير صالح")
                return False
            
            # تحميل الوسائط
            media_content = QMediaContent(file_url)
            self.player.setMedia(media_content)
            
            return True
            
        except Exception as e:
            print(f"خطأ في تحميل الفيديو: {str(e)}")
            return False
    
    def test_with_temp_copy(self):
        """اختبار مع نسخة مؤقتة"""
        if hasattr(self, 'current_file'):
            temp_path = self.create_temp_copy(self.current_file)
            if temp_path:
                print("✅ تم إنشاء النسخة المؤقتة")
                self.info_label.setText(f"اختبار مع نسخة مؤقتة: {os.path.basename(temp_path)}")
                self.load_video(temp_path)
            else:
                print("❌ فشل في إنشاء النسخة المؤقتة")
    
    def toggle_play(self):
        """تبديل التشغيل"""
        if self.player.state() == QMediaPlayer.PlayingState:
            self.player.pause()
            self.play_btn.setText("تشغيل")
        else:
            self.player.play()
            self.play_btn.setText("إيقاف")
    
    def on_media_status_changed(self, status):
        """معالجة تغيير حالة الوسائط"""
        status_names = {
            QMediaPlayer.NoMedia: "لا توجد وسائط",
            QMediaPlayer.LoadingMedia: "جاري التحميل",
            QMediaPlayer.LoadedMedia: "تم التحميل",
            QMediaPlayer.StalledMedia: "متوقف",
            QMediaPlayer.BufferingMedia: "جاري التخزين المؤقت",
            QMediaPlayer.BufferedMedia: "جاهز للتشغيل",
            QMediaPlayer.EndOfMedia: "انتهى الملف",
            QMediaPlayer.InvalidMedia: "ملف غير صالح"
        }
        
        status_name = status_names.get(status, f"غير معروف ({status})")
        print(f"حالة الوسائط: {status_name}")
        
        if status == QMediaPlayer.LoadedMedia:
            self.play_btn.setEnabled(True)
            self.info_label.setText(self.info_label.text() + " - ✅ تم التحميل بنجاح")
        elif status == QMediaPlayer.InvalidMedia:
            self.play_btn.setEnabled(False)
            self.info_label.setText(self.info_label.text() + " - ❌ ملف غير صالح")
    
    def on_error(self, error):
        """معالجة الأخطاء"""
        error_names = {
            QMediaPlayer.NoError: "لا توجد أخطاء",
            QMediaPlayer.ResourceError: "خطأ في المورد",
            QMediaPlayer.FormatError: "خطأ في التنسيق",
            QMediaPlayer.NetworkError: "خطأ في الشبكة",
            QMediaPlayer.AccessDeniedError: "رفض الوصول"
        }
        
        error_name = error_names.get(error, f"خطأ غير معروف ({error})")
        print(f"خطأ في مشغل الوسائط: {error_name}")
        
        if error != QMediaPlayer.NoError:
            self.info_label.setText(self.info_label.text() + f" - ❌ {error_name}")
    
    def closeEvent(self, event):
        """تنظيف عند الإغلاق"""
        # تنظيف الملف المؤقت
        if self.temp_file and os.path.exists(self.temp_file):
            try:
                os.remove(self.temp_file)
                print(f"تم حذف الملف المؤقت: {self.temp_file}")
            except:
                pass
        
        event.accept()

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تعيين اتجاه النص
    app.setLayoutDirection(app.RightToLeft)
    
    window = SimpleVideoTest()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
