#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Comprehensive System Verification for Arabic Media Player with AI Dubbing
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def print_header(title):
    """Print formatted header"""
    print("\n" + "=" * 60)
    print(f"🔍 {title}")
    print("=" * 60)

def print_section(title):
    """Print formatted section"""
    print(f"\n📋 {title}")
    print("-" * 40)

def check_python_environment():
    """Verify Python environment"""
    print_section("Python Environment Check")
    
    python_version = sys.version_info
    print(f"   Python Version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version >= (3, 8):
        print("   ✅ Python version compatible")
        return True
    else:
        print("   ❌ Python version too old (requires 3.8+)")
        return False

def check_dependencies():
    """Check all required dependencies"""
    print_section("Dependencies Check")
    
    required_packages = [
        ("PyQt5", "PyQt5"),
        ("whisper", "openai-whisper"),
        ("transformers", "transformers"),
        ("torch", "torch"),
        ("pydub", "pydub"),
        ("numpy", "numpy"),
        ("gtts", "gtts"),
        ("TTS", "TTS")
    ]
    
    installed = []
    missing = []
    
    for package, pip_name in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package}: Available")
            installed.append(package)
        except ImportError:
            print(f"   ❌ {package}: Missing")
            missing.append(pip_name)
    
    print(f"\n   📊 Status: {len(installed)}/{len(required_packages)} packages available")
    
    if missing:
        print(f"   💡 To install missing packages: pip install {' '.join(missing)}")
    
    return len(missing) == 0

def check_ffmpeg():
    """Check ffmpeg availability"""
    print_section("FFmpeg Check")
    
    try:
        result = subprocess.run(["ffmpeg", "-version"], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"   ✅ {version_line}")
            return True
        else:
            print("   ❌ FFmpeg not working properly")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("   ❌ FFmpeg not found in PATH")
        print("   💡 Install from: https://ffmpeg.org/download.html")
        return False

def check_models():
    """Check AI models availability"""
    print_section("AI Models Check")
    
    models_status = {}
    
    # Check Whisper models
    whisper_paths = [
        Path("models/whisper/medium.pt"),
        Path.home() / ".cache/whisper/medium.pt"
    ]
    
    whisper_found = False
    for path in whisper_paths:
        if path.exists():
            size_mb = path.stat().st_size / (1024 * 1024)
            print(f"   ✅ Whisper medium: Found ({size_mb:.1f} MB) at {path}")
            whisper_found = True
            break
    
    if not whisper_found:
        print("   ❌ Whisper medium: Not found")
    
    models_status['whisper'] = whisper_found
    
    # Check translation models
    translation_dir = Path("models/translation")
    translation_models = ["en-ar", "ar-en", "en-fr"]
    translation_found = 0
    
    for model in translation_models:
        model_path = translation_dir / model
        if model_path.exists() and any(model_path.iterdir()):
            total_size = sum(f.stat().st_size for f in model_path.rglob('*') if f.is_file())
            size_mb = total_size / (1024 * 1024)
            print(f"   ✅ Translation {model}: Found ({size_mb:.1f} MB)")
            translation_found += 1
        else:
            print(f"   ❌ Translation {model}: Not found")
    
    models_status['translation'] = translation_found == len(translation_models)
    
    # Check TTS availability
    try:
        import TTS
        print("   ✅ Coqui TTS: Available")
        models_status['tts'] = True
    except ImportError:
        try:
            import gtts
            print("   ⚠️ Coqui TTS: Not available, gTTS fallback ready")
            models_status['tts'] = True
        except ImportError:
            print("   ❌ TTS: No TTS engines available")
            models_status['tts'] = False
    
    return models_status

def check_file_structure():
    """Check project file structure"""
    print_section("File Structure Check")
    
    required_files = [
        "main.py",
        "تشغيل.bat",
        "core/config.py",
        "core/simple_media_processor.py",
        "core/translation_engine.py",
        "core/tts_engine.py",
        "core/dubbing_engine.py",
        "ui/main_window.py",
        "ui/simple_video_player.py",
        "ui/translation_panel.py"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path}")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def test_application_startup():
    """Test application startup"""
    print_section("Application Startup Test")
    
    try:
        # Test importing main modules
        sys.path.insert(0, '.')
        
        print("   🔄 Testing core imports...")
        from core.config import Config
        from core.simple_media_processor import SimpleMediaProcessor
        from core.translation_engine import TranslationEngine
        from core.tts_engine import TTSEngine
        from core.dubbing_engine import DubbingEngine
        print("   ✅ Core modules import successfully")
        
        print("   🔄 Testing UI imports...")
        from PyQt5.QtWidgets import QApplication
        from ui.main_window import MainWindow
        print("   ✅ UI modules import successfully")
        
        print("   🔄 Testing Qt application creation...")
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        print("   ✅ Qt application created successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Application startup failed: {str(e)}")
        return False

def test_model_loading():
    """Test model loading functionality"""
    print_section("Model Loading Test")
    
    try:
        from core.simple_media_processor import SimpleMediaProcessor
        from core.config import Config
        
        config = Config()
        processor = SimpleMediaProcessor(config)
        
        # Test model checking
        whisper_available = processor.check_whisper_model("medium")
        print(f"   Whisper model check: {'✅ Available' if whisper_available else '❌ Not available'}")
        
        return whisper_available
        
    except Exception as e:
        print(f"   ❌ Model loading test failed: {str(e)}")
        return False

def generate_test_report():
    """Generate comprehensive test report"""
    print_header("COMPREHENSIVE SYSTEM VERIFICATION REPORT")
    
    # Run all checks
    results = {}
    
    results['python'] = check_python_environment()
    results['dependencies'] = check_dependencies()
    results['ffmpeg'] = check_ffmpeg()
    results['models'] = check_models()
    results['files'] = check_file_structure()
    results['startup'] = test_application_startup()
    results['model_loading'] = test_model_loading()
    
    # Generate summary
    print_header("VERIFICATION SUMMARY")
    
    print("📊 Component Status:")
    print(f"   🐍 Python Environment: {'✅ PASS' if results['python'] else '❌ FAIL'}")
    print(f"   📦 Dependencies: {'✅ PASS' if results['dependencies'] else '❌ FAIL'}")
    print(f"   🎬 FFmpeg: {'✅ PASS' if results['ffmpeg'] else '❌ FAIL'}")
    print(f"   🤖 AI Models: {'✅ PASS' if all(results['models'].values()) else '⚠️ PARTIAL'}")
    print(f"   📁 File Structure: {'✅ PASS' if results['files'] else '❌ FAIL'}")
    print(f"   🚀 Application Startup: {'✅ PASS' if results['startup'] else '❌ FAIL'}")
    print(f"   🧠 Model Loading: {'✅ PASS' if results['model_loading'] else '❌ FAIL'}")
    
    # Calculate overall score
    core_components = ['python', 'dependencies', 'ffmpeg', 'files', 'startup']
    core_passed = sum(1 for comp in core_components if results[comp])
    
    models_passed = sum(1 for status in results['models'].values() if status)
    models_total = len(results['models'])
    
    print(f"\n📈 Verification Score:")
    print(f"   Core Components: {core_passed}/{len(core_components)} ({'✅ READY' if core_passed == len(core_components) else '❌ ISSUES'})")
    print(f"   AI Models: {models_passed}/{models_total} ({'✅ COMPLETE' if models_passed == models_total else '⚠️ PARTIAL'})")
    print(f"   Model Loading: {'✅ FUNCTIONAL' if results['model_loading'] else '❌ ISSUES'}")
    
    # Overall status
    ready_for_production = (
        core_passed == len(core_components) and
        results['model_loading'] and
        models_passed >= 2  # At least Whisper and Translation or TTS
    )
    
    print(f"\n🎯 OVERALL STATUS: {'🎉 READY FOR PRODUCTION' if ready_for_production else '⚠️ NEEDS ATTENTION'}")
    
    # Recommendations
    if not ready_for_production:
        print(f"\n💡 RECOMMENDATIONS:")
        
        if not results['python']:
            print("   • Upgrade Python to version 3.8 or higher")
        
        if not results['dependencies']:
            print("   • Install missing dependencies with: pip install -r requirements.txt")
        
        if not results['ffmpeg']:
            print("   • Install FFmpeg and add to system PATH")
        
        if not results['files']:
            print("   • Ensure all project files are present")
        
        if not results['startup']:
            print("   • Fix import errors and dependency issues")
        
        if not all(results['models'].values()):
            print("   • Download missing AI models with: download_models.bat")
        
        if not results['model_loading']:
            print("   • Verify model file integrity and paths")
    
    else:
        print(f"\n🎉 SYSTEM READY!")
        print("   • All core components are functional")
        print("   • AI models are available and loading correctly")
        print("   • Application can start without errors")
        print("   • Ready for end-to-end dubbing workflow")
        print("\n🚀 You can now run the application with: تشغيل.bat")
    
    return ready_for_production

if __name__ == "__main__":
    try:
        success = generate_test_report()
        
        print(f"\n" + "=" * 60)
        print("Press Enter to exit...")
        input()
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Verification interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Verification failed with error: {str(e)}")
        sys.exit(1)
