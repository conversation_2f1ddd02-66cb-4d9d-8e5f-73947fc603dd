#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Download Coqui TTS models for high-quality speech synthesis
"""

import os
import sys
from pathlib import Path

def download_coqui_models():
    """Download essential Coqui TTS models"""
    print("🔊 Downloading Coqui TTS Models for High-Quality Speech")
    print("=" * 60)
    
    try:
        from TTS.api import TTS
        
        # Create TTS models directory
        tts_dir = Path(__file__).parent / "models" / "tts"
        tts_dir.mkdir(parents=True, exist_ok=True)
        
        # Essential models to download
        models_to_download = [
            {
                "name": "Arabic TTS",
                "model": "tts_models/ar/fairseq/tts-transformer",
                "description": "High-quality Arabic speech synthesis"
            },
            {
                "name": "English TTS", 
                "model": "tts_models/en/ljspeech/tacotron2-DDC",
                "description": "High-quality English speech synthesis"
            }
        ]
        
        for model_info in models_to_download:
            print(f"\n📥 Downloading {model_info['name']}...")
            print(f"Model: {model_info['model']}")
            print(f"Description: {model_info['description']}")
            print("-" * 40)
            
            try:
                # Initialize TTS with the model (this downloads it)
                tts = TTS(model_name=model_info['model'], progress_bar=True)
                print(f"✅ Successfully downloaded {model_info['name']}")
                
                # Test the model with a simple phrase
                test_text = "مرحبا" if "ar" in model_info['model'] else "Hello"
                test_file = tts_dir / f"test_{model_info['name'].lower().replace(' ', '_')}.wav"
                
                print(f"🧪 Testing model with: '{test_text}'")
                tts.tts_to_file(text=test_text, file_path=str(test_file))
                
                if test_file.exists():
                    file_size = test_file.stat().st_size
                    print(f"✅ Test successful - Generated {file_size} bytes audio file")
                    # Clean up test file
                    test_file.unlink()
                else:
                    print("⚠️ Test file not generated, but model downloaded")
                    
            except Exception as e:
                print(f"❌ Failed to download {model_info['name']}: {str(e)}")
                continue
        
        print("\n" + "=" * 60)
        print("📊 TTS Models Download Summary:")
        print("=" * 60)
        
        # Check what's actually cached
        cache_dir = Path.home() / ".cache" / "tts"
        if cache_dir.exists():
            total_size = sum(f.stat().st_size for f in cache_dir.rglob('*') if f.is_file())
            size_mb = total_size / (1024 * 1024)
            print(f"✅ TTS models cached: {size_mb:.1f} MB")
            print(f"📁 Cache location: {cache_dir}")
        else:
            print("⚠️ No TTS cache directory found")
        
        print("\n🎉 TTS models download completed!")
        print("The application will now use high-quality Coqui TTS for speech synthesis.")
        
        return True
        
    except ImportError:
        print("❌ TTS library not available")
        print("Please install it with: pip install TTS")
        return False
    except Exception as e:
        print(f"❌ Error downloading TTS models: {str(e)}")
        return False

if __name__ == "__main__":
    success = download_coqui_models()
    
    if success:
        print("\n✅ All TTS models are ready for use!")
    else:
        print("\n⚠️ Some TTS models may not be available, but gTTS will work as fallback")
    
    print("\nPress Enter to exit...")
    input()
