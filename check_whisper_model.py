#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت للتحقق من وجود نموذج Whisper ونسخه إلى المكان الصحيح
"""

import os
import shutil
import glob

def find_whisper_model():
    """البحث عن نموذج Whisper في الجهاز"""
    print("جاري البحث عن نموذج Whisper في الجهاز...")
    
    # أماكن محتملة للبحث
    search_paths = [
        os.path.expanduser("~"),  # مجلد المستخدم
        "C:\\",  # القرص C
        "D:\\",  # القرص D (إذا كان موجوداً)
        os.path.join(os.path.expanduser("~"), "Downloads"),  # مجلد التنزيلات
        os.path.join(os.path.expanduser("~"), "Desktop"),  # سطح المكتب
    ]
    
    found_models = []
    
    for search_path in search_paths:
        if os.path.exists(search_path):
            print(f"البحث في: {search_path}")
            try:
                # البحث عن ملفات medium.pt
                pattern = os.path.join(search_path, "**", "medium.pt")
                matches = glob.glob(pattern, recursive=True)
                found_models.extend(matches)
                
                # البحث عن ملفات تحتوي على whisper في اسمها
                pattern = os.path.join(search_path, "**", "*whisper*medium*")
                matches = glob.glob(pattern, recursive=True)
                found_models.extend(matches)
                
            except Exception as e:
                print(f"خطأ في البحث في {search_path}: {str(e)}")
    
    # إزالة التكرارات
    found_models = list(set(found_models))
    
    return found_models

def get_whisper_cache_path():
    """الحصول على مسار مجلد تخزين نماذج Whisper"""
    cache_dir = os.path.join(os.path.expanduser("~"), ".cache", "whisper")
    return cache_dir

def check_model_size(file_path):
    """التحقق من حجم النموذج"""
    try:
        size = os.path.getsize(file_path)
        size_mb = size / (1024 * 1024)
        
        # نموذج medium يجب أن يكون حوالي 1500 ميجابايت
        if size_mb > 1000:  # أكثر من 1 جيجابايت
            return True, size_mb
        else:
            return False, size_mb
    except:
        return False, 0

def copy_model_to_cache(source_path, target_dir):
    """نسخ النموذج إلى مجلد التخزين المؤقت"""
    try:
        # إنشاء المجلد إذا لم يكن موجوداً
        os.makedirs(target_dir, exist_ok=True)
        
        # مسار الهدف
        target_path = os.path.join(target_dir, "medium.pt")
        
        # نسخ الملف
        print(f"جاري نسخ النموذج من {source_path} إلى {target_path}")
        shutil.copy2(source_path, target_path)
        
        print("تم نسخ النموذج بنجاح!")
        return True
    except Exception as e:
        print(f"خطأ في نسخ النموذج: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("أداة التحقق من نموذج Whisper")
    print("=" * 60)
    
    # التحقق من وجود النموذج في المكان الصحيح
    cache_dir = get_whisper_cache_path()
    target_path = os.path.join(cache_dir, "medium.pt")
    
    print(f"المكان المتوقع للنموذج: {target_path}")
    
    if os.path.exists(target_path):
        is_valid, size_mb = check_model_size(target_path)
        if is_valid:
            print(f"✓ تم العثور على نموذج Whisper صحيح (الحجم: {size_mb:.2f} ميجابايت)")
            print("النموذج جاهز للاستخدام!")
            return
        else:
            print(f"✗ تم العثور على ملف ولكن حجمه صغير جداً ({size_mb:.2f} ميجابايت)")
            print("سيتم البحث عن نموذج آخر...")
    else:
        print("✗ لم يتم العثور على النموذج في المكان المتوقع")
    
    # البحث عن النموذج في الجهاز
    found_models = find_whisper_model()
    
    if not found_models:
        print("\n✗ لم يتم العثور على أي نموذج Whisper في الجهاز")
        print("\nيمكنك:")
        print("1. تشغيل ملف download_whisper_model.bat لتنزيل النموذج")
        print("2. تنزيل النموذج يدوياً من:")
        print("   https://openaipublic.azureedge.net/main/whisper/models/345ae4da62f9b3d59415adc60127b97c714f32e89e936602e85993674d08dcb1/medium.pt")
        return
    
    print(f"\n✓ تم العثور على {len(found_models)} ملف محتمل:")
    
    valid_models = []
    for i, model_path in enumerate(found_models):
        is_valid, size_mb = check_model_size(model_path)
        print(f"{i+1}. {model_path}")
        print(f"   الحجم: {size_mb:.2f} ميجابايت - {'صحيح' if is_valid else 'صغير جداً'}")
        
        if is_valid:
            valid_models.append(model_path)
    
    if not valid_models:
        print("\n✗ لم يتم العثور على أي نموذج صحيح")
        return
    
    # اختيار أفضل نموذج (الأكبر حجماً)
    best_model = max(valid_models, key=lambda x: os.path.getsize(x))
    
    print(f"\n✓ سيتم استخدام النموذج: {best_model}")
    
    # نسخ النموذج إلى المكان الصحيح
    if copy_model_to_cache(best_model, cache_dir):
        print("\n✓ تم إعداد نموذج Whisper بنجاح!")
        print("يمكنك الآن تشغيل البرنامج واستخدام وظيفة استخراج النص.")
    else:
        print("\n✗ فشل في إعداد النموذج")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
