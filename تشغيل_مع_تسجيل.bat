@echo off
title مشغل الوسائط مع تسجيل مفصل

echo ============================================================
echo مشغل الوسائط مع تسجيل مفصل لكل العمليات
echo ============================================================
echo.

REM إنشاء ملف لتسجيل كل شيء
set LOG_FILE=debug_log.txt
echo [%date% %time%] بدء تشغيل البرنامج > %LOG_FILE%

REM التحقق من وجود Python
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ❌ خطأ: لم يتم العثور على Python
    echo [%date% %time%] خطأ: Python غير موجود >> %LOG_FILE%
    pause
    exit /b 1
)

echo ✅ Python متاح
echo [%date% %time%] Python متاح >> %LOG_FILE%

REM التحقق من OpenCV
python -c "import cv2; print('OpenCV version:', cv2.__version__)" 2>>%LOG_FILE%
if %ERRORLEVEL% neq 0 (
    echo ⚠️ OpenCV غير متاح
    echo [%date% %time%] OpenCV غير متاح >> %LOG_FILE%
) else (
    echo ✅ OpenCV متاح
    echo [%date% %time%] OpenCV متاح >> %LOG_FILE%
)

echo.
echo 🚀 تشغيل البرنامج مع تسجيل مفصل...
echo 📝 سيتم حفظ كل التفاصيل في: %LOG_FILE%
echo.

REM تشغيل البرنامج مع تسجيل كل شيء
python main.py 2>&1 | tee -a %LOG_FILE%

REM إذا فشل التشغيل
if %ERRORLEVEL% neq 0 (
    echo.
    echo ============================================================
    echo ❌ خطأ في تشغيل البرنامج!
    echo ============================================================
    echo.
    echo تم حفظ تفاصيل الخطأ في: %LOG_FILE%
    echo.
    echo آخر 10 أسطر من ملف السجل:
    echo ----------------------------------------
    tail -10 %LOG_FILE% 2>nul || (
        echo لا يمكن عرض ملف السجل
        echo افتح %LOG_FILE% يدوياً لرؤية التفاصيل
    )
    echo ----------------------------------------
    echo.
    pause
) else (
    echo.
    echo ✅ تم إغلاق البرنامج بنجاح
    echo [%date% %time%] تم إغلاق البرنامج بنجاح >> %LOG_FILE%
)

echo.
echo 📝 ملف السجل الكامل محفوظ في: %LOG_FILE%
pause
