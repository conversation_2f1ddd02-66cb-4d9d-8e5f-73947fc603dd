#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
محرك الدبلجة - دمج الصوت المولد مع الفيديو
"""

import os
import subprocess
import tempfile
import numpy as np
from pydub import AudioSegment

class DubbingEngine:
    """محرك دمج الصوت مع الفيديو"""

    def __init__(self, config):
        """تهيئة محرك الدبلجة"""
        self.config = config

    def dub_video(self, input_video, audio_track, output_video, mix_original=True,
                  original_volume=0.3, new_audio_volume=1.0, progress_callback=None):
        """دمج مسار الصوت الجديد مع الفيديو"""
        try:
            if progress_callback:
                progress_callback(0, "جاري تحضير الدبلجة...")

            # التحقق من وجود الملفات
            if not os.path.exists(input_video):
                if progress_callback:
                    progress_callback(0, f"ملف الفيديو غير موجود: {input_video}")
                return None

            if isinstance(audio_track, str):
                if not os.path.exists(audio_track):
                    if progress_callback:
                        progress_callback(0, f"ملف الصوت غير موجود: {audio_track}")
                    return None
                # تحميل الصوت من الملف
                new_audio = AudioSegment.from_file(audio_track)
            elif isinstance(audio_track, AudioSegment):
                new_audio = audio_track
            else:
                if progress_callback:
                    progress_callback(0, "نوع مسار الصوت غير مدعوم")
                return None

            if progress_callback:
                progress_callback(10, "جاري استخراج الصوت الأصلي...")

            # استخراج الصوت الأصلي من الفيديو
            original_audio_path = self._extract_original_audio(input_video, progress_callback)
            if not original_audio_path:
                # إذا فشل استخراج الصوت، استخدم الصوت الجديد فقط
                return self._replace_audio_only(input_video, new_audio, output_video, progress_callback)

            if progress_callback:
                progress_callback(30, "جاري معالجة الصوت...")

            # معالجة ودمج الصوت
            final_audio_path = self._process_and_mix_audio(
                original_audio_path, new_audio, mix_original,
                original_volume, new_audio_volume, progress_callback
            )

            if not final_audio_path:
                if progress_callback:
                    progress_callback(0, "فشل في معالجة الصوت")
                return None

            if progress_callback:
                progress_callback(70, "جاري دمج الصوت مع الفيديو...")

            # دمج الصوت النهائي مع الفيديو
            success = self._merge_audio_with_video(input_video, final_audio_path, output_video, progress_callback)

            # تنظيف الملفات المؤقتة
            self._cleanup_temp_files([original_audio_path, final_audio_path])

            if success:
                if progress_callback:
                    progress_callback(100, "تم إنجاز الدبلجة بنجاح")
                return output_video
            else:
                if progress_callback:
                    progress_callback(0, "فشل في دمج الصوت مع الفيديو")
                return None

        except Exception as e:
            if progress_callback:
                progress_callback(0, f"خطأ في الدبلجة: {str(e)}")
            return None

    def _extract_original_audio(self, video_path, progress_callback=None):
        """استخراج الصوت الأصلي من الفيديو"""
        try:
            # إنشاء ملف مؤقت للصوت
            temp_audio = tempfile.NamedTemporaryFile(suffix=".wav", delete=False)
            temp_audio_path = temp_audio.name
            temp_audio.close()

            # استخراج الصوت باستخدام ffmpeg
            cmd = [
                "ffmpeg", "-i", video_path,
                "-vn",  # بدون فيديو
                "-acodec", "pcm_s16le",  # ترميز صوتي غير مضغوط
                "-ar", "44100",  # معدل العينات
                "-ac", "2",  # قناتان صوتيتان
                "-y",  # استبدال الملف إذا كان موجوداً
                temp_audio_path
            ]

            # تشغيل الأمر
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                if progress_callback:
                    progress_callback(25, "تم استخراج الصوت الأصلي")
                return temp_audio_path
            else:
                if progress_callback:
                    progress_callback(0, f"فشل في استخراج الصوت: {result.stderr}")
                return None

        except Exception as e:
            if progress_callback:
                progress_callback(0, f"خطأ في استخراج الصوت: {str(e)}")
            return None

    def _process_and_mix_audio(self, original_audio_path, new_audio, mix_original,
                              original_volume, new_audio_volume, progress_callback=None):
        """معالجة ودمج الصوت الأصلي مع الجديد"""
        try:
            if progress_callback:
                progress_callback(35, "جاري تحميل الصوت الأصلي...")

            # تحميل الصوت الأصلي
            original_audio = AudioSegment.from_file(original_audio_path)

            if progress_callback:
                progress_callback(45, "جاري معالجة الصوت الجديد...")

            # تعديل مستوى صوت المسار الجديد
            new_audio = new_audio + (20 * np.log10(new_audio_volume))

            # مطابقة طول الصوت الجديد مع الأصلي
            if len(new_audio) < len(original_audio):
                # تكرار الصوت الجديد إذا كان أقصر
                repetitions = (len(original_audio) // len(new_audio)) + 1
                new_audio = new_audio * repetitions

            # قطع الصوت الجديد ليطابق طول الأصلي
            new_audio = new_audio[:len(original_audio)]

            if progress_callback:
                progress_callback(55, "جاري دمج المسارات الصوتية...")

            if mix_original:
                # تقليل مستوى الصوت الأصلي
                original_audio = original_audio + (20 * np.log10(original_volume))
                # دمج المسارين
                final_audio = original_audio.overlay(new_audio)
            else:
                # استبدال الصوت الأصلي بالجديد
                final_audio = new_audio

            # حفظ الصوت النهائي
            temp_final = tempfile.NamedTemporaryFile(suffix=".wav", delete=False)
            final_audio_path = temp_final.name
            temp_final.close()

            final_audio.export(final_audio_path, format="wav")

            if progress_callback:
                progress_callback(65, "تم دمج المسارات الصوتية")

            return final_audio_path

        except Exception as e:
            if progress_callback:
                progress_callback(0, f"خطأ في معالجة الصوت: {str(e)}")
            return None

    def _replace_audio_only(self, input_video, new_audio, output_video, progress_callback=None):
        """استبدال الصوت فقط (بدون دمج)"""
        try:
            if progress_callback:
                progress_callback(40, "جاري حفظ الصوت الجديد...")

            # حفظ الصوت الجديد كملف مؤقت
            temp_audio = tempfile.NamedTemporaryFile(suffix=".wav", delete=False)
            temp_audio_path = temp_audio.name
            temp_audio.close()

            new_audio.export(temp_audio_path, format="wav")

            if progress_callback:
                progress_callback(60, "جاري دمج الصوت مع الفيديو...")

            # دمج الصوت مع الفيديو
            success = self._merge_audio_with_video(input_video, temp_audio_path, output_video, progress_callback)

            # تنظيف الملف المؤقت
            self._cleanup_temp_files([temp_audio_path])

            return output_video if success else None

        except Exception as e:
            if progress_callback:
                progress_callback(0, f"خطأ في استبدال الصوت: {str(e)}")
            return None

    def _merge_audio_with_video(self, video_path, audio_path, output_path, progress_callback=None):
        """دمج الصوت مع الفيديو باستخدام ffmpeg"""
        try:
            # إنشاء أمر ffmpeg
            cmd = [
                "ffmpeg",
                "-i", video_path,  # ملف الفيديو
                "-i", audio_path,  # ملف الصوت
                "-c:v", "copy",  # نسخ الفيديو بدون إعادة ترميز
                "-c:a", "aac",  # ترميز الصوت إلى AAC
                "-map", "0:v:0",  # استخدام الفيديو من الملف الأول
                "-map", "1:a:0",  # استخدام الصوت من الملف الثاني
                "-shortest",  # استخدام أقصر مدة
                "-y",  # استبدال الملف إذا كان موجوداً
                output_path
            ]

            # تشغيل الأمر
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                if progress_callback:
                    progress_callback(90, "تم دمج الصوت مع الفيديو")
                return True
            else:
                if progress_callback:
                    progress_callback(0, f"فشل في دمج الصوت مع الفيديو: {result.stderr}")
                return False

        except Exception as e:
            if progress_callback:
                progress_callback(0, f"خطأ في دمج الصوت مع الفيديو: {str(e)}")
            return False

    def _cleanup_temp_files(self, file_paths):
        """تنظيف الملفات المؤقتة"""
        for file_path in file_paths:
            try:
                if file_path and os.path.exists(file_path):
                    os.remove(file_path)
            except:
                pass  # تجاهل أخطاء الحذف
