@echo off
title مشغل الوسائط مع الدبلجة الآلية

echo ============================================================
echo مشغل الوسائط مع الدبلجة الآلية
echo ============================================================
echo.

REM التحقق من وجود Python
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ❌ خطأ: لم يتم العثور على Python
    echo.
    echo يرجى تثبيت Python من https://www.python.org/downloads/
    echo تأكد من إضافة Python إلى PATH أثناء التثبيت
    echo.
    pause
    exit /b 1
)

REM التحقق من وجود الملف الرئيسي
if not exist "main.py" (
    echo ❌ خطأ: لم يتم العثور على main.py
    echo.
    echo تأكد من تشغيل الملف من مجلد البرنامج الصحيح
    echo.
    pause
    exit /b 1
)

echo ✅ Python متاح
echo ✅ ملفات البرنامج موجودة
echo.

REM فحص سريع للنماذج
echo 🔍 فحص سريع للنماذج...

REM فحص نموذج Whisper في المجلد المحلي أو cache
set WHISPER_FOUND=0
if exist "models\whisper\medium.pt" (
    echo ✅ نموذج Whisper medium موجود محلياً
    set WHISPER_FOUND=1
) else if exist "%USERPROFILE%\.cache\whisper\medium.pt" (
    echo ✅ نموذج Whisper medium موجود في cache
    set WHISPER_FOUND=1
) else (
    echo ⚠️ نموذج Whisper medium غير موجود - سيتم تنزيله عند الحاجة
)

if exist "models\translation" (
    echo ✅ نماذج الترجمة موجودة
) else (
    echo ⚠️ نماذج الترجمة غير موجودة - سيتم تنزيلها عند الحاجة
)

echo.
echo 🚀 جاري تشغيل البرنامج...
echo.

REM التحقق من OpenCV
python -c "import cv2" >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ⚠️ OpenCV غير مثبت - قد تواجه مشاكل في تشغيل الفيديو
    echo 💡 لحل مشاكل الفيديو، شغل: install_opencv.bat
    echo.
    echo هل تريد تثبيت OpenCV الآن؟ (y/n)
    set /p install_opencv=
    if /i "%install_opencv%"=="y" (
        echo.
        echo 🔄 تثبيت OpenCV...
        call install_opencv.bat
        echo.
        echo ✅ تم تثبيت OpenCV، سيتم إعادة تشغيل البرنامج...
        echo.
    )
) else (
    echo ✅ OpenCV متاح - سيتم استخدام مشغل فيديو محسن
    echo.
)

REM تشغيل البرنامج
python main.py

REM إذا فشل التشغيل
if %ERRORLEVEL% neq 0 (
    echo.
    echo ============================================================
    echo ❌ خطأ في تشغيل البرنامج!
    echo ============================================================
    echo.
    echo الأسباب المحتملة:
    echo • المكتبات المطلوبة غير مثبتة
    echo • إصدار Python غير متوافق (يتطلب Python 3.8+)
    echo • مشكلة في ملفات البرنامج
    echo.
    echo الحلول المقترحة:
    echo • شغل install_requirements.bat لتثبيت المكتبات
    echo • تأكد من تثبيت Python 3.8 أو أحدث
    echo • شغل python check_models.py للتشخيص المفصل
    echo.
    echo للمساعدة، راجع ملف README_ARABIC.txt
    echo ============================================================
    pause
) else (
    echo.
    echo ✅ تم إغلاق البرنامج بنجاح
)
