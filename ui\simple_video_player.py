#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مشغل فيديو مبسط وفعال
"""

import os
import sys
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QSlider, QLabel, QStyle, QFrame)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QSize, QUrl
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtMultimediaWidgets import QVideoWidget

class SimpleVideoPlayer(QWidget):
    """مشغل فيديو مبسط"""

    # الإشارات
    media_loaded = pyqtSignal(bool)
    playback_state_changed = pyqtSignal(bool)

    def __init__(self, config):
        """تهيئة مشغل الفيديو"""
        super().__init__()

        self.config = config
        self.current_file = None
        self.temp_file_path = None  # مسار الملف المؤقت
        self.is_playing = False

        self._init_ui()
        self._init_player()
        self._connect_signals()

    def _init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # مكون عرض الفيديو
        self.video_widget = QVideoWidget()
        self.video_widget.setMinimumSize(320, 180)
        layout.addWidget(self.video_widget)

        # شريط التحكم
        control_layout = QHBoxLayout()
        control_layout.setContentsMargins(5, 5, 5, 5)

        # زر التشغيل/الإيقاف
        self.play_button = QPushButton()
        self.play_button.setIcon(self.style().standardIcon(QStyle.SP_MediaPlay))
        self.play_button.setIconSize(QSize(24, 24))
        self.play_button.setToolTip("تشغيل/إيقاف")
        control_layout.addWidget(self.play_button)

        # زر الإيقاف
        self.stop_button = QPushButton()
        self.stop_button.setIcon(self.style().standardIcon(QStyle.SP_MediaStop))
        self.stop_button.setIconSize(QSize(24, 24))
        self.stop_button.setToolTip("إيقاف")
        control_layout.addWidget(self.stop_button)

        # شريط التقدم
        self.position_slider = QSlider(Qt.Horizontal)
        self.position_slider.setRange(0, 1000)
        self.position_slider.setValue(0)
        self.position_slider.setToolTip("موضع التشغيل")
        control_layout.addWidget(self.position_slider)

        # عرض الوقت
        self.time_label = QLabel("00:00 / 00:00")
        self.time_label.setMinimumWidth(100)
        self.time_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        control_layout.addWidget(self.time_label)

        # زر كتم الصوت
        self.mute_button = QPushButton()
        self.mute_button.setIcon(self.style().standardIcon(QStyle.SP_MediaVolume))
        self.mute_button.setIconSize(QSize(24, 24))
        self.mute_button.setToolTip("كتم الصوت")
        control_layout.addWidget(self.mute_button)

        # شريط مستوى الصوت
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(70)
        self.volume_slider.setMaximumWidth(100)
        self.volume_slider.setToolTip("مستوى الصوت")
        control_layout.addWidget(self.volume_slider)

        layout.addLayout(control_layout)

    def _init_player(self):
        """تهيئة مشغل الوسائط"""
        self.player = QMediaPlayer(None, QMediaPlayer.VideoSurface)
        self.player.setVideoOutput(self.video_widget)

        # ربط إشارات المشغل
        self.player.stateChanged.connect(self._on_state_changed)
        self.player.positionChanged.connect(self._on_position_changed)
        self.player.durationChanged.connect(self._on_duration_changed)
        self.player.mediaStatusChanged.connect(self._on_media_status_changed)
        self.player.error.connect(self._on_error)

    def _connect_signals(self):
        """ربط إشارات واجهة المستخدم"""
        self.play_button.clicked.connect(self.toggle_play)
        self.stop_button.clicked.connect(self.stop)
        self.mute_button.clicked.connect(self.toggle_mute)

        self.position_slider.sliderMoved.connect(self.set_position)
        self.volume_slider.valueChanged.connect(self.set_volume)

    def _create_temp_copy_if_needed(self, file_path):
        """إنشاء نسخة مؤقتة إذا كان المسار يحتوي على أحرف عربية"""
        try:
            # التحقق من وجود أحرف عربية في المسار
            has_arabic = any(ord(char) > 127 for char in file_path)

            if not has_arabic:
                return file_path  # لا حاجة لنسخة مؤقتة

            print(f"🔄 المسار يحتوي على أحرف عربية، إنشاء نسخة مؤقتة...")

            import tempfile
            import shutil

            # إنشاء مجلد مؤقت
            temp_dir = tempfile.mkdtemp(prefix="video_temp_")

            # الحصول على امتداد الملف
            file_ext = os.path.splitext(file_path)[1]

            # إنشاء اسم ملف مؤقت بسيط
            temp_filename = f"temp_video{file_ext}"
            temp_path = os.path.join(temp_dir, temp_filename)

            # نسخ الملف
            shutil.copy2(file_path, temp_path)

            if os.path.exists(temp_path):
                print(f"✅ تم إنشاء نسخة مؤقتة: {temp_path}")
                # حفظ مسار النسخة المؤقتة للتنظيف لاحقاً
                self.temp_file_path = temp_path
                return temp_path
            else:
                print("❌ فشل في إنشاء النسخة المؤقتة")
                return file_path

        except Exception as e:
            print(f"❌ خطأ في إنشاء النسخة المؤقتة: {str(e)}")
            return file_path

    def load_media(self, file_path):
        """تحميل ملف وسائط - نسخة مبسطة"""
        print(f"🔄 تحميل الملف: {file_path}")

        if not os.path.exists(file_path):
            print(f"❌ الملف غير موجود: {file_path}")
            self.media_loaded.emit(False)
            return

        self.current_file = file_path

        try:
            # تحميل مباشر بدون تعقيدات
            file_url = QUrl.fromLocalFile(os.path.abspath(file_path))
            media_content = QMediaContent(file_url)
            self.player.setMedia(media_content)

            # إعادة تعيين واجهة المستخدم
            self.position_slider.setValue(0)
            self.time_label.setText("00:00 / 00:00")

            print(f"✅ تم تحميل الملف: {os.path.basename(file_path)}")

        except Exception as e:
            print(f"❌ خطأ في تحميل الملف: {str(e)}")
            self.media_loaded.emit(False)

    def toggle_play(self):
        """تبديل حالة التشغيل"""
        if not self.current_file:
            return

        if self.player.state() == QMediaPlayer.PlayingState:
            self.player.pause()
        else:
            self.player.play()

    def stop(self):
        """إيقاف التشغيل"""
        self.player.stop()
        self.position_slider.setValue(0)

    def toggle_mute(self):
        """تبديل كتم الصوت"""
        self.player.setMuted(not self.player.isMuted())

        if self.player.isMuted():
            self.mute_button.setIcon(self.style().standardIcon(QStyle.SP_MediaVolumeMuted))
        else:
            self.mute_button.setIcon(self.style().standardIcon(QStyle.SP_MediaVolume))

    def set_position(self, position):
        """تعيين موضع التشغيل"""
        if self.player.duration() > 0:
            self.player.setPosition(int(position * self.player.duration() / 1000))

    def set_volume(self, volume):
        """تعيين مستوى الصوت"""
        self.player.setVolume(volume)
        self.config.set("player", "volume", volume)

    def set_position(self, position):
        """تعيين موضع التشغيل"""
        if self.player.duration() > 0:
            new_position = int(position * self.player.duration() / 1000)
            print(f"🔄 تغيير الموضع إلى: {new_position} من {self.player.duration()}")
            self.player.setPosition(new_position)

    def toggle_fullscreen(self):
        """تبديل وضع ملء الشاشة"""
        if self.video_widget.isFullScreen():
            self.video_widget.showNormal()
        else:
            self.video_widget.showFullScreen()

    def _on_state_changed(self, state):
        """معالجة تغيير حالة المشغل"""
        if state == QMediaPlayer.PlayingState:
            self.play_button.setIcon(self.style().standardIcon(QStyle.SP_MediaPause))
            self.is_playing = True
            self.playback_state_changed.emit(True)
        else:
            self.play_button.setIcon(self.style().standardIcon(QStyle.SP_MediaPlay))
            self.is_playing = False
            self.playback_state_changed.emit(False)

    def _on_position_changed(self, position):
        """معالجة تغيير موضع التشغيل"""
        if self.player.duration() > 0:
            slider_value = int(position * 1000 / self.player.duration())
            self.position_slider.blockSignals(True)
            self.position_slider.setValue(slider_value)
            self.position_slider.blockSignals(False)

            # تحديث عرض الوقت
            position_str = self._format_time(position)
            duration_str = self._format_time(self.player.duration())
            self.time_label.setText(f"{position_str} / {duration_str}")

    def _on_duration_changed(self, duration):
        """معالجة تغيير مدة الوسائط"""
        duration_str = self._format_time(duration)
        self.time_label.setText(f"00:00 / {duration_str}")

    def _on_media_status_changed(self, status):
        """معالجة تغيير حالة الوسائط"""
        status_messages = {
            QMediaPlayer.NoMedia: "لا يوجد وسائط",
            QMediaPlayer.LoadingMedia: "جاري تحميل الوسائط...",
            QMediaPlayer.LoadedMedia: "تم تحميل الوسائط بنجاح",
            QMediaPlayer.StalledMedia: "توقف تحميل الوسائط",
            QMediaPlayer.BufferingMedia: "جاري التخزين المؤقت...",
            QMediaPlayer.BufferedMedia: "تم التخزين المؤقت",
            QMediaPlayer.EndOfMedia: "انتهت الوسائط",
            QMediaPlayer.InvalidMedia: "ملف وسائط غير صالح"
        }

        message = status_messages.get(status, f"حالة غير معروفة: {status}")
        print(f"حالة الوسائط: {message}")

        if status == QMediaPlayer.LoadedMedia:
            self.media_loaded.emit(True)
        elif status == QMediaPlayer.InvalidMedia:
            self.media_loaded.emit(False)
            print("تأكد من أن الملف صالح ومدعوم")
        elif status == QMediaPlayer.EndOfMedia:
            # إعادة تعيين موضع التشغيل عند انتهاء الفيديو
            self.player.setPosition(0)

    def _on_error(self, error):
        """معالجة أخطاء المشغل"""
        error_messages = {
            QMediaPlayer.NoError: "لا يوجد خطأ",
            QMediaPlayer.ResourceError: "خطأ في المورد - تأكد من وجود الملف",
            QMediaPlayer.FormatError: "تنسيق الملف غير مدعوم - جرب ملف MP4",
            QMediaPlayer.NetworkError: "خطأ في الشبكة",
            QMediaPlayer.AccessDeniedError: "ممنوع الوصول للملف - تأكد من الصلاحيات"
        }

        error_msg = error_messages.get(error, f"خطأ غير معروف: {error}")
        print(f"خطأ في مشغل الوسائط: {error_msg}")

        # اقتراحات للحل
        if error == QMediaPlayer.FormatError:
            print("💡 نصيحة: جرب تحويل الفيديو إلى MP4 باستخدام برنامج تحويل")
        elif error == QMediaPlayer.ResourceError:
            print("💡 نصيحة: تأكد من أن مسار الملف صحيح ولا يحتوي على أحرف خاصة")

        self.media_loaded.emit(False)

    def _format_time(self, milliseconds):
        """تنسيق الوقت"""
        seconds = int(milliseconds / 1000)
        minutes = seconds // 60
        seconds %= 60
        return f"{minutes:02d}:{seconds:02d}"

    def keyPressEvent(self, event):
        """معالجة أحداث المفاتيح"""
        key = event.key()

        if key == Qt.Key_Space:
            self.toggle_play()
        elif key == Qt.Key_M:
            self.toggle_mute()
        elif key == Qt.Key_Right:
            # تقديم 5 ثوانٍ
            current_pos = self.player.position()
            new_pos = min(self.player.duration(), current_pos + 5000)
            self.player.setPosition(new_pos)
        elif key == Qt.Key_Left:
            # إرجاع 5 ثوانٍ
            current_pos = self.player.position()
            new_pos = max(0, current_pos - 5000)
            self.player.setPosition(new_pos)
        elif key == Qt.Key_Up:
            # رفع مستوى الصوت
            current_volume = self.volume_slider.value()
            self.volume_slider.setValue(min(100, current_volume + 5))
        elif key == Qt.Key_Down:
            # خفض مستوى الصوت
            current_volume = self.volume_slider.value()
            self.volume_slider.setValue(max(0, current_volume - 5))
        else:
            super().keyPressEvent(event)
